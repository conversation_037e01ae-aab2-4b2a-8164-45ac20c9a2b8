package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppNotice;
import com.ruoyi.fuguang.service.IAppNoticeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP通知管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController("appNoticeManageController")
@RequestMapping("/fuguang/notice")
public class AppNoticeManageController extends BaseController
{
    @Autowired
    private IAppNoticeService appNoticeService;

    /**
     * 查询APP通知列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppNotice appNotice)
    {
        startPage();
        List<AppNotice> list = appNoticeService.selectAppNoticeList(appNotice);
        return getDataTable(list);
    }

    /**
     * 导出APP通知列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:export')")
    @Log(title = "APP通知", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppNotice appNotice)
    {
        List<AppNotice> list = appNoticeService.selectAppNoticeList(appNotice);
        ExcelUtil<AppNotice> util = new ExcelUtil<AppNotice>(AppNotice.class);
        util.exportExcel(response, list, "APP通知数据");
    }

    /**
     * 获取APP通知详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId)
    {
        return success(appNoticeService.selectAppNoticeByNoticeId(noticeId));
    }

    /**
     * 新增APP通知
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:add')")
    @Log(title = "APP通知", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppNotice appNotice)
    {
        appNotice.setCreateBy(getUsername());
        return toAjax(appNoticeService.insertAppNotice(appNotice));
    }

    /**
     * 修改APP通知
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:edit')")
    @Log(title = "APP通知", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppNotice appNotice)
    {
        appNotice.setUpdateBy(getUsername());
        return toAjax(appNoticeService.updateAppNotice(appNotice));
    }

    /**
     * 删除APP通知
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:remove')")
    @Log(title = "APP通知", businessType = BusinessType.DELETE)
	@DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        return toAjax(appNoticeService.deleteAppNoticeByNoticeIds(noticeIds));
    }

    /**
     * 发送系统通知
     */
    @PreAuthorize("@ss.hasPermi('fuguang:notice:send')")
    @Log(title = "发送系统通知", businessType = BusinessType.INSERT)
    @PostMapping("/send")
    public AjaxResult sendNotice(@RequestBody AppNotice appNotice)
    {
        int result = appNoticeService.sendSystemNotice(
            appNotice.getNoticeTitle(), 
            appNotice.getNoticeContent(), 
            appNotice.getTargetType(), 
            appNotice.getTargetUserId()
        );
        return toAjax(result);
    }
}
