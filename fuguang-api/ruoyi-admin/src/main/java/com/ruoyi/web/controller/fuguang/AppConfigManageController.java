package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppConfig;
import com.ruoyi.fuguang.service.IAppConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP配置管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController("appConfigManageController")
@RequestMapping("/fuguang/config")
public class AppConfigManageController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 查询APP配置列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppConfig appConfig)
    {
        startPage();
        List<AppConfig> list = appConfigService.selectAppConfigList(appConfig);
        return getDataTable(list);
    }

    /**
     * 导出APP配置列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:export')")
    @Log(title = "APP配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppConfig appConfig)
    {
        List<AppConfig> list = appConfigService.selectAppConfigList(appConfig);
        ExcelUtil<AppConfig> util = new ExcelUtil<AppConfig>(AppConfig.class);
        util.exportExcel(response, list, "APP配置数据");
    }

    /**
     * 获取APP配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return success(appConfigService.selectAppConfigByConfigId(configId));
    }

    /**
     * 新增APP配置
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:add')")
    @Log(title = "APP配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppConfig appConfig)
    {
        if (!appConfigService.checkConfigKeyUnique(appConfig))
        {
            return error("新增配置'" + appConfig.getConfigName() + "'失败，配置键名已存在");
        }
        appConfig.setCreateBy(getUsername());
        return toAjax(appConfigService.insertAppConfig(appConfig));
    }

    /**
     * 修改APP配置
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:edit')")
    @Log(title = "APP配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppConfig appConfig)
    {
        if (!appConfigService.checkConfigKeyUnique(appConfig))
        {
            return error("修改配置'" + appConfig.getConfigName() + "'失败，配置键名已存在");
        }
        appConfig.setUpdateBy(getUsername());
        return toAjax(appConfigService.updateAppConfig(appConfig));
    }

    /**
     * 删除APP配置
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:remove')")
    @Log(title = "APP配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(appConfigService.deleteAppConfigByConfigIds(configIds));
    }

    /**
     * 根据配置键名查询配置值
     */
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        return success(appConfigService.selectConfigValueByKey(configKey));
    }

    /**
     * 刷新配置缓存
     */
    @PreAuthorize("@ss.hasPermi('fuguang:config:remove')")
    @Log(title = "APP配置", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        // TODO: 实现配置缓存刷新逻辑
        return success();
    }
}
