package com.ruoyi.web.controller.fuguang;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * APP用户管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController("appUserManageController")
@RequestMapping("/fuguang/appuser")
public class AppUserManageController extends BaseController
{
    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询APP用户列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUser appUser)
    {
        startPage();
        List<AppUser> list = appUserService.selectAppUserList(appUser);
        return getDataTable(list);
    }

    /**
     * 导出APP用户列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:export')")
    @Log(title = "APP用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUser appUser)
    {
        List<AppUser> list = appUserService.selectAppUserList(appUser);
        ExcelUtil<AppUser> util = new ExcelUtil<AppUser>(AppUser.class);
        util.exportExcel(response, list, "APP用户数据");
    }

    /**
     * 获取APP用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(appUserService.selectAppUserByUserId(userId));
    }

    /**
     * 新增APP用户
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:add')")
    @Log(title = "APP用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUser appUser)
    {
        if (!appUserService.checkUserNameUnique(appUser))
        {
            return error("新增用户'" + appUser.getUserName() + "'失败，登录账号已存在");
        }
        else if (!appUserService.checkPhoneUnique(appUser))
        {
            return error("新增用户'" + appUser.getUserName() + "'失败，手机号码已存在");
        }
        else if (!appUserService.checkEmailUnique(appUser))
        {
            return error("新增用户'" + appUser.getUserName() + "'失败，邮箱账号已存在");
        }
        return toAjax(appUserService.insertAppUser(appUser));
    }

    /**
     * 修改APP用户
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:edit')")
    @Log(title = "APP用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUser appUser)
    {
        if (!appUserService.checkUserNameUnique(appUser))
        {
            return error("修改用户'" + appUser.getUserName() + "'失败，登录账号已存在");
        }
        else if (!appUserService.checkPhoneUnique(appUser))
        {
            return error("修改用户'" + appUser.getUserName() + "'失败，手机号码已存在");
        }
        else if (!appUserService.checkEmailUnique(appUser))
        {
            return error("修改用户'" + appUser.getUserName() + "'失败，邮箱账号已存在");
        }
        return toAjax(appUserService.updateAppUser(appUser));
    }

    /**
     * 删除APP用户
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:remove')")
    @Log(title = "APP用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(appUserService.deleteAppUserByUserIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:resetPwd')")
    @Log(title = "APP用户", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody AppUser appUser)
    {
        appUserService.resetUserPwd(appUser.getUserName(), appUser.getPassword());
        return success();
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('fuguang:appuser:edit')")
    @Log(title = "APP用户", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AppUser appUser)
    {
        return toAjax(appUserService.updateUserProfile(appUser));
    }
}
