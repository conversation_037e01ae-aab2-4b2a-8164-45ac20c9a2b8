-- 协议管理菜单 SQL
-- 添加浮光管理一级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('浮光管理', '0', '5', 'fuguang', null, 1, 0, 'M', '0', '0', '', 'system', 'admin', sysdate(), '', null, '浮光APP管理目录');

-- 获取浮光管理菜单ID
SELECT @fuguangMenuId := LAST_INSERT_ID();

-- 添加协议管理二级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('协议管理', @fuguangMenuId, '1', 'agreement', 'fuguang/agreement/index', 1, 0, 'C', '0', '0', 'fuguang:config:list', 'documentation', 'admin', sysdate(), '', null, '协议管理菜单');

-- 获取协议管理菜单ID
SELECT @agreementMenuId := LAST_INSERT_ID();

-- 添加协议管理按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('协议查询', @agreementMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'fuguang:config:query', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('协议新增', @agreementMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'fuguang:config:add', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('协议修改', @agreementMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'fuguang:config:edit', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('协议删除', @agreementMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'fuguang:config:remove', '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('协议导出', @agreementMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'fuguang:config:export', '#', 'admin', sysdate(), '', null, '');
