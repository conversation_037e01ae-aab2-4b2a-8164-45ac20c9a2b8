-- 初始化协议数据
-- 如果app_config表不存在，先创建表
CREATE TABLE IF NOT EXISTS `app_config` (
  `config_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置主键',
  `config_name` varchar(100) DEFAULT '' COMMENT '配置名称',
  `config_key` varchar(100) DEFAULT '' COMMENT '配置键名',
  `config_value` text COMMENT '配置键值',
  `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='APP配置信息表';

-- 删除已存在的协议配置（如果有的话）
DELETE FROM app_config WHERE config_key IN ('app.privacy.policy', 'app.user.agreement');

-- 插入用户协议
INSERT INTO app_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES 
('用户协议', 'app.user.agreement', '# 用户协议

欢迎使用浮光APP！

## 1. 服务条款
本协议是您与浮光APP之间关于使用浮光APP服务的法律协议。

## 2. 用户权利和义务
2.1 您有权使用浮光APP提供的各项服务
2.2 您应当遵守相关法律法规，不得利用本服务从事违法活动

## 3. 服务内容
3.1 任务发布与接取服务
3.2 用户信息管理服务
3.3 其他相关服务

## 4. 隐私保护
我们重视您的隐私保护，详细内容请参阅《隐私协议》

## 5. 免责声明
5.1 平台仅提供信息撮合服务
5.2 用户自行承担交易风险

## 6. 协议修改
我们保留随时修改本协议的权利，修改后的协议将在APP内公布。

如有疑问，请联系客服：400-888-8888', 'Y', 'admin', NOW(), 'APP用户协议配置');

-- 插入隐私协议
INSERT INTO app_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES 
('隐私协议', 'app.privacy.policy', '# 隐私协议

感谢您使用浮光APP，我们非常重视您的个人信息和隐私保护。

## 1. 信息收集
我们会收集您在使用浮光APP时提供的信息，包括但不限于：
- 注册信息：用户名、密码、手机号、邮箱等
- 个人资料：头像、昵称、性别、地址等
- 位置信息：用于任务匹配和地理位置服务
- 设备信息：设备型号、操作系统版本等

## 2. 信息使用
我们使用收集的信息用于：
- 提供和改进我们的服务
- 处理您的任务发布和接取请求
- 发送重要通知和服务更新
- 保护用户安全和防止欺诈

## 3. 信息保护
我们采取适当的安全措施保护您的个人信息：
- 使用加密技术保护数据传输
- 限制员工访问个人信息
- 定期审查安全措施
- 遵守相关法律法规要求

## 4. 信息共享
除法律要求外，我们不会向第三方分享您的个人信息。

## 5. 您的权利
您有权访问、更正、删除您的个人信息。

## 6. 联系我们
如果您对本隐私政策有任何疑问，请联系我们：
- 邮箱：<EMAIL>
- 电话：400-888-8888', 'Y', 'admin', NOW(), 'APP隐私协议配置');
