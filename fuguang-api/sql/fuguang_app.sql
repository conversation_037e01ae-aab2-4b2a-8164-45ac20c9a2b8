-- ----------------------------
-- 浮光壁垒APP数据库表结构
-- ----------------------------

-- ----------------------------
-- 1、APP用户表
-- ----------------------------
drop table if exists app_user;
create table app_user (
  user_id           bigint(20)      not null auto_increment    comment '用户ID',
  user_name         varchar(30)     not null                   comment '用户账号',
  nick_name         varchar(30)     not null                   comment '用户昵称',
  email             varchar(50)     default ''                 comment '用户邮箱',
  phonenumber       varchar(11)     default ''                 comment '手机号码',
  sex               char(1)         default '0'                comment '用户性别（0男 1女 2未知）',
  avatar            varchar(100)    default ''                 comment '头像地址',
  password          varchar(100)    default ''                 comment '密码',
  status            char(1)         default '0'                comment '帐号状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  login_ip          varchar(128)    default ''                 comment '最后登录IP',
  login_date        datetime                                   comment '最后登录时间',
  user_type         char(1)         default '0'                comment '用户类型（0普通用户 1商家用户）',
  auth_status       char(1)         default '0'                comment '实名认证状态（0未认证 1已认证）',
  real_name         varchar(30)     default ''                 comment '真实姓名',
  id_card           varchar(18)     default ''                 comment '身份证号',
  address           varchar(200)    default ''                 comment '地址',
  longitude         varchar(20)     default ''                 comment '经度',
  latitude          varchar(20)     default ''                 comment '纬度',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (user_id),
  unique key uk_user_name (user_name),
  key idx_phonenumber (phonenumber),
  key idx_email (email)
) engine=innodb auto_increment=1000 comment = 'APP用户信息表';

-- ----------------------------
-- 2、APP任务表
-- ----------------------------
drop table if exists app_task;
create table app_task (
  task_id           bigint(20)      not null auto_increment    comment '任务ID',
  task_title        varchar(100)    not null                   comment '任务标题',
  task_desc         text                                       comment '任务描述',
  task_amount       decimal(10,2)   default 0.00               comment '任务金额',
  task_type         char(1)         default '0'                comment '任务类型（0普通任务 1紧急任务）',
  task_status       char(1)         default '0'                comment '任务状态（0待接取 1进行中 2已完成 3已取消）',
  publisher_id      bigint(20)      not null                   comment '发布者ID',
  publisher_name    varchar(30)     default ''                 comment '发布者昵称',
  publisher_avatar  varchar(100)    default ''                 comment '发布者头像',
  receiver_id       bigint(20)      default null               comment '接收者ID',
  receiver_name     varchar(30)     default ''                 comment '接收者昵称',
  task_address      varchar(200)    default ''                 comment '任务地址',
  longitude         varchar(20)     default ''                 comment '经度',
  latitude          varchar(20)     default ''                 comment '纬度',
  start_time        datetime                                   comment '开始时间',
  end_time          datetime                                   comment '结束时间',
  view_count        int(11)         default 0                  comment '浏览次数',
  hot_score         int(11)         default 0                  comment '热度分数',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (task_id),
  key idx_publisher_id (publisher_id),
  key idx_receiver_id (receiver_id),
  key idx_task_status (task_status),
  key idx_hot_score (hot_score),
  key idx_create_time (create_time)
) engine=innodb auto_increment=1000 comment = 'APP任务表';

-- ----------------------------
-- 3、APP通知表
-- ----------------------------
drop table if exists app_notice;
create table app_notice (
  notice_id         bigint(20)      not null auto_increment    comment '通知ID',
  notice_title      varchar(100)    not null                   comment '通知标题',
  notice_content    text                                       comment '通知内容',
  notice_type       char(1)         default '0'                comment '通知类型（0系统通知 1活动通知 2任务通知）',
  notice_status     char(1)         default '0'                comment '通知状态（0正常 1关闭）',
  target_type       char(1)         default '0'                comment '目标类型（0全部用户 1指定用户）',
  target_user_id    bigint(20)      default null               comment '目标用户ID',
  is_read           char(1)         default '0'                comment '是否已读（0未读 1已读）',
  publish_time      datetime                                   comment '发布时间',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (notice_id),
  key idx_target_user_id (target_user_id),
  key idx_notice_type (notice_type),
  key idx_publish_time (publish_time)
) engine=innodb auto_increment=1000 comment = 'APP通知表';

-- ----------------------------
-- 4、APP配置表
-- ----------------------------
drop table if exists app_config;
create table app_config (
  config_id         bigint(20)      not null auto_increment    comment '配置ID',
  config_name       varchar(100)    not null                   comment '配置名称',
  config_key        varchar(100)    not null                   comment '配置键名',
  config_value      text                                       comment '配置键值',
  config_type       char(1)         default 'N'                comment '系统内置（Y是 N否）',
  config_desc       varchar(500)    default ''                 comment '配置描述',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (config_id),
  unique key uk_config_key (config_key)
) engine=innodb auto_increment=1000 comment = 'APP配置表';

-- ----------------------------
-- 5、APP功能配置表
-- ----------------------------
drop table if exists app_function;
create table app_function (
  function_id       bigint(20)      not null auto_increment    comment '功能ID',
  function_name     varchar(50)     not null                   comment '功能名称',
  function_icon     varchar(100)    default ''                 comment '功能图标',
  function_url      varchar(200)    default ''                 comment '功能链接',
  function_type     char(1)         default '0'                comment '功能类型（0内部页面 1外部链接）',
  sort_order        int(4)          default 0                  comment '显示顺序',
  status            char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (function_id),
  key idx_sort_order (sort_order)
) engine=innodb auto_increment=1000 comment = 'APP功能配置表';

-- ----------------------------
-- 6、兴业助农配置表
-- ----------------------------
drop table if exists app_agriculture;
create table app_agriculture (
  agriculture_id    bigint(20)      not null auto_increment    comment '配置ID',
  title             varchar(100)    not null                   comment '标题',
  image_url         varchar(200)    default ''                 comment '图片地址',
  content           text                                       comment '内容',
  link_url          varchar(200)    default ''                 comment '链接地址',
  status            char(1)         default '0'                comment '状态（0正常 1停用）',
  sort_order        int(4)          default 0                  comment '显示顺序',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (agriculture_id),
  key idx_sort_order (sort_order)
) engine=innodb auto_increment=1000 comment = '兴业助农配置表';

-- ----------------------------
-- 初始化APP配置数据
-- ----------------------------
insert into app_config values(1, '隐私协议', 'app.privacy.policy', '这里是隐私协议内容...', 'Y', 'APP隐私协议配置', 'admin', sysdate(), '', null, '隐私协议配置');
insert into app_config values(2, '用户协议', 'app.user.agreement', '这里是用户协议内容...', 'Y', 'APP用户协议配置', 'admin', sysdate(), '', null, '用户协议配置');
insert into app_config values(3, '首页标语', 'app.home.slogan', '链接你我，共创未来', 'Y', 'APP首页标语配置', 'admin', sysdate(), '', null, '首页标语配置');
insert into app_config values(4, '客服电话', 'app.service.phone', '************', 'Y', 'APP客服电话配置', 'admin', sysdate(), '', null, '客服电话配置');

-- ----------------------------
-- 初始化APP功能配置数据
-- ----------------------------
insert into app_function values(1, '我的钱包', 'icon-wallet', '/pages/wallet/index', '0', 1, '0', 'admin', sysdate(), '', null, '我的钱包功能');
insert into app_function values(2, '我的任务', 'icon-task', '/pages/task/my', '0', 2, '0', 'admin', sysdate(), '', null, '我的任务功能');
insert into app_function values(3, '实名认证', 'icon-auth', '/pages/auth/index', '0', 3, '0', 'admin', sysdate(), '', null, '实名认证功能');
insert into app_function values(4, '客服中心', 'icon-service', '/pages/service/index', '0', 4, '0', 'admin', sysdate(), '', null, '客服中心功能');
insert into app_function values(5, '设置', 'icon-setting', '/pages/setting/index', '0', 5, '0', 'admin', sysdate(), '', null, '设置功能');

-- ----------------------------
-- 初始化兴业助农配置数据
-- ----------------------------
insert into app_agriculture values(1, '助农扶贫', '/static/images/agriculture1.jpg', '助力农业发展，扶持贫困地区', '/pages/agriculture/detail?id=1', '0', 1, 'admin', sysdate(), '', null, '助农扶贫配置');

-- ----------------------------
-- 初始化测试数据
-- ----------------------------
-- 插入测试APP用户
insert into app_user values(1000, 'appuser1', '测试用户1', '<EMAIL>', '13800138001', '0', '', '$2a$10$7JB720yubVSOfvam/RdTWuH.lkw3g5ewjhKTjOmjbqmKiMILddKpe', '0', '0', '127.0.0.1', sysdate(), '0', '0', '', '', '', '', '', 'admin', sysdate(), '', null, '测试用户1');
insert into app_user values(1001, 'appuser2', '测试用户2', '<EMAIL>', '13800138002', '1', '', '$2a$10$7JB720yubVSOfvam/RdTWuH.lkw3g5ewjhKTjOmjbqmKiMILddKpe', '0', '0', '127.0.0.1', sysdate(), '0', '0', '', '', '', '', '', 'admin', sysdate(), '', null, '测试用户2');

-- 插入测试任务
insert into app_task values(1000, '帮忙取快递', '帮忙到菜鸟驿站取快递，地址在学校门口', 5.00, '0', '0', 1000, '测试用户1', '', null, '', '北京市海淀区', '116.3', '39.9', sysdate(), date_add(sysdate(), interval 1 day), 0, 0, 'admin', sysdate(), '', null, '帮忙取快递任务');
insert into app_task values(1001, '代买午餐', '帮忙买一份午餐，麦当劳或肯德基都可以', 10.00, '0', '0', 1001, '测试用户2', '', null, '', '北京市朝阳区', '116.4', '39.9', sysdate(), date_add(sysdate(), interval 2 hour), 0, 0, 'admin', sysdate(), '', null, '代买午餐任务');

-- 插入测试通知
insert into app_notice values(1000, '欢迎使用浮光壁垒APP', '欢迎您使用浮光壁垒APP，这里有丰富的任务等您来完成！', '0', '0', '0', null, '0', sysdate(), 'admin', sysdate(), '', null, '欢迎通知');
insert into app_notice values(1001, '系统维护通知', '系统将于今晚23:00-01:00进行维护，请合理安排时间', '0', '0', '0', null, '0', sysdate(), 'admin', sysdate(), '', null, '维护通知');
