package com.ruoyi.app.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.UserStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.service.IAppUserService;

/**
 * APP用户验证处理
 *
 * <AUTHOR>
 */
@Service("appUserDetailsService")
public class AppUserDetailsServiceImpl implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(AppUserDetailsServiceImpl.class);

    @Autowired
    private IAppUserService appUserService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException
    {
        AppUser appUser = appUserService.selectUserByUserName(username);
        if (StringUtils.isNull(appUser))
        {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        }
        else if (UserStatus.DISABLE.getCode().equals(appUser.getStatus()))
        {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }

        return createLoginUser(appUser);
    }

    public UserDetails createLoginUser(AppUser appUser)
    {
        // 将AppUser转换为SysUser以兼容现有的LoginUser
        SysUser sysUser = new SysUser();
        sysUser.setUserId(appUser.getUserId());
        sysUser.setUserName(appUser.getUserName());
        sysUser.setNickName(appUser.getNickName());
        sysUser.setEmail(appUser.getEmail());
        sysUser.setPhonenumber(appUser.getPhonenumber());
        sysUser.setSex(appUser.getSex());
        sysUser.setAvatar(appUser.getAvatar());
        sysUser.setPassword(appUser.getPassword());
        sysUser.setStatus(appUser.getStatus());
        sysUser.setDelFlag(appUser.getDelFlag());
        sysUser.setLoginIp(appUser.getLoginIp());
        sysUser.setLoginDate(appUser.getLoginDate());
        sysUser.setCreateBy(appUser.getCreateBy());
        sysUser.setCreateTime(appUser.getCreateTime());
        sysUser.setUpdateBy(appUser.getUpdateBy());
        sysUser.setUpdateTime(appUser.getUpdateTime());
        sysUser.setRemark(appUser.getRemark());
        
        // APP用户默认没有部门和角色
        sysUser.setDeptId(null);

        return new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, null);
    }
}
