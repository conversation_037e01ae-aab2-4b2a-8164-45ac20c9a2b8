package com.ruoyi.app.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.service.IAppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP协议接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP协议接口")
@RestController("appAgreementController")
@RequestMapping("/app/agreement")
public class AppAgreementController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 获取用户协议
     *
     * @return 用户协议内容
     */
    @Anonymous
    @ApiOperation("获取用户协议")
    @GetMapping("/user")
    public AjaxResult getUserAgreement()
    {
        try {
            String userAgreement = appConfigService.getUserAgreement();
            if (userAgreement == null || userAgreement.isEmpty()) {
                return success("用户协议内容暂未配置，请联系管理员在后台配置协议内容。");
            }
            return success("请求成功",userAgreement);
        } catch (Exception e) {
            return error("获取用户协议失败：" + e.getMessage());
        }
    }

    /**
     * 获取隐私协议
     *
     * @return 隐私协议内容
     */
    @Anonymous
    @ApiOperation("获取隐私协议")
    @GetMapping("/privacy")
    public AjaxResult getPrivacyPolicy()
    {
        try {
            String privacyPolicy = appConfigService.getPrivacyPolicy();
            if (privacyPolicy == null || privacyPolicy.isEmpty()) {
                return success("隐私协议内容暂未配置，请联系管理员在后台配置协议内容。");
            }
            return success("请求成功",privacyPolicy);
        } catch (Exception e) {
            return error("获取隐私协议失败：" + e.getMessage());
        }
    }

    /**
     * 测试接口连通性
     *
     * @return 测试结果
     */
    @Anonymous
    @ApiOperation("测试协议接口")
    @GetMapping("/test")
    public AjaxResult test()
    {
        return success("协议接口连通正常，当前时间：" + System.currentTimeMillis());
    }
}
