package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP通知对象 app_notice
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public class AppNotice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 通知ID */
    private Long noticeId;

    /** 通知标题 */
    @Excel(name = "通知标题")
    private String noticeTitle;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String noticeContent;

    /** 通知类型（0系统通知 1活动通知 2任务通知） */
    @Excel(name = "通知类型", readConverterExp = "0=系统通知,1=活动通知,2=任务通知")
    private String noticeType;

    /** 通知状态（0正常 1关闭） */
    @Excel(name = "通知状态", readConverterExp = "0=正常,1=关闭")
    private String noticeStatus;

    /** 目标类型（0全部用户 1指定用户） */
    @Excel(name = "目标类型", readConverterExp = "0=全部用户,1=指定用户")
    private String targetType;

    /** 目标用户ID */
    @Excel(name = "目标用户ID")
    private Long targetUserId;

    /** 是否已读（0未读 1已读） */
    @Excel(name = "是否已读", readConverterExp = "0=未读,1=已读")
    private String isRead;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    public void setNoticeId(Long noticeId) 
    {
        this.noticeId = noticeId;
    }

    public Long getNoticeId() 
    {
        return noticeId;
    }

    public void setNoticeTitle(String noticeTitle) 
    {
        this.noticeTitle = noticeTitle;
    }

    public String getNoticeTitle() 
    {
        return noticeTitle;
    }

    public void setNoticeContent(String noticeContent) 
    {
        this.noticeContent = noticeContent;
    }

    public String getNoticeContent() 
    {
        return noticeContent;
    }

    public void setNoticeType(String noticeType) 
    {
        this.noticeType = noticeType;
    }

    public String getNoticeType() 
    {
        return noticeType;
    }

    public void setNoticeStatus(String noticeStatus) 
    {
        this.noticeStatus = noticeStatus;
    }

    public String getNoticeStatus() 
    {
        return noticeStatus;
    }

    public void setTargetType(String targetType) 
    {
        this.targetType = targetType;
    }

    public String getTargetType() 
    {
        return targetType;
    }

    public void setTargetUserId(Long targetUserId) 
    {
        this.targetUserId = targetUserId;
    }

    public Long getTargetUserId() 
    {
        return targetUserId;
    }

    public void setIsRead(String isRead) 
    {
        this.isRead = isRead;
    }

    public String getIsRead() 
    {
        return isRead;
    }

    public void setPublishTime(Date publishTime) 
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() 
    {
        return publishTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("noticeId", getNoticeId())
            .append("noticeTitle", getNoticeTitle())
            .append("noticeContent", getNoticeContent())
            .append("noticeType", getNoticeType())
            .append("noticeStatus", getNoticeStatus())
            .append("targetType", getTargetType())
            .append("targetUserId", getTargetUserId())
            .append("isRead", getIsRead())
            .append("publishTime", getPublishTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
