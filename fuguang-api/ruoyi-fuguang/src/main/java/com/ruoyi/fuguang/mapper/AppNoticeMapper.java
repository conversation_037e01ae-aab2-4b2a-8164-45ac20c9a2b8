package com.ruoyi.fuguang.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.fuguang.domain.AppNotice;

/**
 * APP通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface AppNoticeMapper 
{
    /**
     * 查询APP通知
     * 
     * @param noticeId APP通知主键
     * @return APP通知
     */
    public AppNotice selectAppNoticeByNoticeId(Long noticeId);

    /**
     * 查询APP通知列表
     * 
     * @param appNotice APP通知
     * @return APP通知集合
     */
    public List<AppNotice> selectAppNoticeList(AppNotice appNotice);

    /**
     * 新增APP通知
     * 
     * @param appNotice APP通知
     * @return 结果
     */
    public int insertAppNotice(AppNotice appNotice);

    /**
     * 修改APP通知
     * 
     * @param appNotice APP通知
     * @return 结果
     */
    public int updateAppNotice(AppNotice appNotice);

    /**
     * 删除APP通知
     * 
     * @param noticeId APP通知主键
     * @return 结果
     */
    public int deleteAppNoticeByNoticeId(Long noticeId);

    /**
     * 批量删除APP通知
     * 
     * @param noticeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppNoticeByNoticeIds(Long[] noticeIds);

    /**
     * 查询用户通知列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 通知列表
     */
    public List<AppNotice> selectNoticesByUser(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询最新系统通知
     * 
     * @param limit 限制数量
     * @return 通知列表
     */
    public List<AppNotice> selectLatestSystemNotices(@Param("limit") Integer limit);

    /**
     * 标记通知为已读
     * 
     * @param noticeId 通知ID
     * @param userId 用户ID
     * @return 结果
     */
    public int markNoticeAsRead(@Param("noticeId") Long noticeId, @Param("userId") Long userId);

    /**
     * 查询未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读数量
     */
    public int countUnreadNotices(Long userId);
}
