package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.mapper.AppConfigMapper;
import com.ruoyi.fuguang.domain.AppConfig;
import com.ruoyi.fuguang.service.IAppConfigService;

/**
 * APP配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class AppConfigServiceImpl implements IAppConfigService 
{
    @Autowired
    private AppConfigMapper appConfigMapper;

    /**
     * 查询APP配置
     * 
     * @param configId APP配置主键
     * @return APP配置
     */
    @Override
    public AppConfig selectAppConfigByConfigId(Long configId)
    {
        return appConfigMapper.selectAppConfigByConfigId(configId);
    }

    /**
     * 根据配置键查询配置
     * 
     * @param configKey 配置键
     * @return APP配置
     */
    @Override
    public AppConfig selectAppConfigByKey(String configKey)
    {
        return appConfigMapper.selectAppConfigByKey(configKey);
    }

    /**
     * 查询APP配置列表
     * 
     * @param appConfig APP配置
     * @return APP配置
     */
    @Override
    public List<AppConfig> selectAppConfigList(AppConfig appConfig)
    {
        return appConfigMapper.selectAppConfigList(appConfig);
    }

    /**
     * 新增APP配置
     * 
     * @param appConfig APP配置
     * @return 结果
     */
    @Override
    public int insertAppConfig(AppConfig appConfig)
    {
        appConfig.setCreateTime(DateUtils.getNowDate());
        return appConfigMapper.insertAppConfig(appConfig);
    }

    /**
     * 修改APP配置
     * 
     * @param appConfig APP配置
     * @return 结果
     */
    @Override
    public int updateAppConfig(AppConfig appConfig)
    {
        appConfig.setUpdateTime(DateUtils.getNowDate());
        return appConfigMapper.updateAppConfig(appConfig);
    }

    /**
     * 批量删除APP配置
     * 
     * @param configIds 需要删除的APP配置主键
     * @return 结果
     */
    @Override
    public int deleteAppConfigByConfigIds(Long[] configIds)
    {
        return appConfigMapper.deleteAppConfigByConfigIds(configIds);
    }

    /**
     * 删除APP配置信息
     * 
     * @param configId APP配置主键
     * @return 结果
     */
    @Override
    public int deleteAppConfigByConfigId(Long configId)
    {
        return appConfigMapper.deleteAppConfigByConfigId(configId);
    }

    /**
     * 根据配置键获取配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    public String selectConfigValueByKey(String configKey)
    {
        AppConfig config = appConfigMapper.selectAppConfigByKey(configKey);
        return StringUtils.isNotNull(config) ? config.getConfigValue() : StringUtils.EMPTY;
    }

    /**
     * 校验配置键名是否唯一
     * 
     * @param appConfig 配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(AppConfig appConfig)
    {
        Long configId = StringUtils.isNull(appConfig.getConfigId()) ? -1L : appConfig.getConfigId();
        AppConfig info = appConfigMapper.checkConfigKeyUnique(appConfig.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 获取隐私协议
     * 
     * @return 隐私协议内容
     */
    @Override
    public String getPrivacyPolicy()
    {
        return selectConfigValueByKey("app.privacy.policy");
    }

    /**
     * 获取用户协议
     * 
     * @return 用户协议内容
     */
    @Override
    public String getUserAgreement()
    {
        return selectConfigValueByKey("app.user.agreement");
    }

    /**
     * 获取首页标语
     * 
     * @return 首页标语
     */
    @Override
    public String getHomeSlogan()
    {
        String slogan = selectConfigValueByKey("app.home.slogan");
        return StringUtils.isNotEmpty(slogan) ? slogan : "链接你我，共创未来";
    }

    /**
     * 获取客服电话
     * 
     * @return 客服电话
     */
    @Override
    public String getServicePhone()
    {
        return selectConfigValueByKey("app.service.phone");
    }
}
