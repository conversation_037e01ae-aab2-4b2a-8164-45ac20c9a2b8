package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.AppUser;

/**
 * APP用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface IAppUserService 
{
    /**
     * 查询APP用户
     * 
     * @param userId APP用户主键
     * @return APP用户
     */
    public AppUser selectAppUserByUserId(Long userId);

    /**
     * 根据用户名查询APP用户
     * 
     * @param userName 用户名
     * @return APP用户
     */
    public AppUser selectAppUserByUserName(String userName);

    /**
     * 查询APP用户列表
     * 
     * @param appUser APP用户
     * @return APP用户集合
     */
    public List<AppUser> selectAppUserList(AppUser appUser);

    /**
     * 新增APP用户
     * 
     * @param appUser APP用户
     * @return 结果
     */
    public int insertAppUser(AppUser appUser);

    /**
     * 修改APP用户
     * 
     * @param appUser APP用户
     * @return 结果
     */
    public int updateAppUser(AppUser appUser);

    /**
     * 批量删除APP用户
     * 
     * @param userIds 需要删除的APP用户主键集合
     * @return 结果
     */
    public int deleteAppUserByUserIds(Long[] userIds);

    /**
     * 删除APP用户信息
     * 
     * @param userId APP用户主键
     * @return 结果
     */
    public int deleteAppUserByUserId(Long userId);

    /**
     * 校验用户名称是否唯一
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(AppUser appUser);

    /**
     * 校验手机号码是否唯一
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(AppUser appUser);

    /**
     * 校验email是否唯一
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    public boolean checkEmailUnique(AppUser appUser);

    /**
     * 用户注册
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    public boolean registerUser(AppUser appUser);

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    public AppUser selectUserByUserName(String userName);

    /**
     * 通过手机号查询用户
     * 
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    public AppUser selectUserByPhonenumber(String phonenumber);

    /**
     * 修改用户基本信息
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    public int updateUserProfile(AppUser appUser);

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);
}
