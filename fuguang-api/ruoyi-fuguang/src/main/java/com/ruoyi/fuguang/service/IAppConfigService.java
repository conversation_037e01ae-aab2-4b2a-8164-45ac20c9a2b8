package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.AppConfig;

/**
 * APP配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface IAppConfigService 
{
    /**
     * 查询APP配置
     * 
     * @param configId APP配置主键
     * @return APP配置
     */
    public AppConfig selectAppConfigByConfigId(Long configId);

    /**
     * 根据配置键查询配置
     * 
     * @param configKey 配置键
     * @return APP配置
     */
    public AppConfig selectAppConfigByKey(String configKey);

    /**
     * 查询APP配置列表
     * 
     * @param appConfig APP配置
     * @return APP配置集合
     */
    public List<AppConfig> selectAppConfigList(AppConfig appConfig);

    /**
     * 新增APP配置
     * 
     * @param appConfig APP配置
     * @return 结果
     */
    public int insertAppConfig(AppConfig appConfig);

    /**
     * 修改APP配置
     * 
     * @param appConfig APP配置
     * @return 结果
     */
    public int updateAppConfig(AppConfig appConfig);

    /**
     * 批量删除APP配置
     * 
     * @param configIds 需要删除的APP配置主键集合
     * @return 结果
     */
    public int deleteAppConfigByConfigIds(Long[] configIds);

    /**
     * 删除APP配置信息
     * 
     * @param configId APP配置主键
     * @return 结果
     */
    public int deleteAppConfigByConfigId(Long configId);

    /**
     * 根据配置键获取配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    public String selectConfigValueByKey(String configKey);

    /**
     * 校验配置键名是否唯一
     * 
     * @param appConfig 配置信息
     * @return 结果
     */
    public boolean checkConfigKeyUnique(AppConfig appConfig);

    /**
     * 获取隐私协议
     * 
     * @return 隐私协议内容
     */
    public String getPrivacyPolicy();

    /**
     * 获取用户协议
     * 
     * @return 用户协议内容
     */
    public String getUserAgreement();

    /**
     * 获取首页标语
     * 
     * @return 首页标语
     */
    public String getHomeSlogan();

    /**
     * 获取客服电话
     * 
     * @return 客服电话
     */
    public String getServicePhone();
}
