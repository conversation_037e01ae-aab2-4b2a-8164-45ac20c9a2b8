package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.fuguang.mapper.AppTaskMapper;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.fuguang.domain.AppUser;

/**
 * APP任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class AppTaskServiceImpl implements IAppTaskService 
{
    @Autowired
    private AppTaskMapper appTaskMapper;

    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询APP任务
     * 
     * @param taskId APP任务主键
     * @return APP任务
     */
    @Override
    public AppTask selectAppTaskByTaskId(Long taskId)
    {
        return appTaskMapper.selectAppTaskByTaskId(taskId);
    }

    /**
     * 查询APP任务列表
     * 
     * @param appTask APP任务
     * @return APP任务
     */
    @Override
    public List<AppTask> selectAppTaskList(AppTask appTask)
    {
        return appTaskMapper.selectAppTaskList(appTask);
    }

    /**
     * 新增APP任务
     * 
     * @param appTask APP任务
     * @return 结果
     */
    @Override
    public int insertAppTask(AppTask appTask)
    {
        // 设置发布者信息
        if (appTask.getPublisherId() != null) {
            AppUser publisher = appUserService.selectAppUserByUserId(appTask.getPublisherId());
            if (publisher != null) {
                appTask.setPublisherName(publisher.getNickName());
                appTask.setPublisherAvatar(publisher.getAvatar());
            }
        }
        
        appTask.setTaskStatus("0"); // 待接取
        appTask.setViewCount(0);
        appTask.setHotScore(0);
        appTask.setCreateTime(DateUtils.getNowDate());
        return appTaskMapper.insertAppTask(appTask);
    }

    /**
     * 修改APP任务
     * 
     * @param appTask APP任务
     * @return 结果
     */
    @Override
    public int updateAppTask(AppTask appTask)
    {
        appTask.setUpdateTime(DateUtils.getNowDate());
        return appTaskMapper.updateAppTask(appTask);
    }

    /**
     * 批量删除APP任务
     * 
     * @param taskIds 需要删除的APP任务主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskByTaskIds(Long[] taskIds)
    {
        return appTaskMapper.deleteAppTaskByTaskIds(taskIds);
    }

    /**
     * 删除APP任务信息
     * 
     * @param taskId APP任务主键
     * @return 结果
     */
    @Override
    public int deleteAppTaskByTaskId(Long taskId)
    {
        return appTaskMapper.deleteAppTaskByTaskId(taskId);
    }

    /**
     * 查询热门任务列表
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param limit 限制数量
     * @return 任务列表
     */
    @Override
    public List<AppTask> selectHotTaskList(String longitude, String latitude, Integer limit)
    {
        return appTaskMapper.selectHotTaskList(longitude, latitude, limit);
    }

    /**
     * 增加任务浏览次数
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long taskId)
    {
        return appTaskMapper.increaseViewCount(taskId);
    }

    /**
     * 接取任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int acceptTask(Long taskId, Long userId)
    {
        // 获取用户信息
        AppUser user = appUserService.selectAppUserByUserId(userId);
        String userName = user != null ? user.getNickName() : "";
        
        return appTaskMapper.acceptTask(taskId, userId, userName);
    }

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int completeTask(Long taskId)
    {
        return appTaskMapper.completeTask(taskId);
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int cancelTask(Long taskId)
    {
        return appTaskMapper.cancelTask(taskId);
    }

    /**
     * 查询用户发布的任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    @Override
    public List<AppTask> selectTasksByPublisher(Long userId)
    {
        return appTaskMapper.selectTasksByPublisher(userId);
    }

    /**
     * 查询用户接取的任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    @Override
    public List<AppTask> selectTasksByReceiver(Long userId)
    {
        return appTaskMapper.selectTasksByReceiver(userId);
    }
}
