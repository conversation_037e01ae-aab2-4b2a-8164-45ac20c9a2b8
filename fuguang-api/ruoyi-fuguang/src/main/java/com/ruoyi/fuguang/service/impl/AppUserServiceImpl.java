package com.ruoyi.fuguang.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.mapper.AppUserMapper;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.service.IAppUserService;

/**
 * APP用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Service
public class AppUserServiceImpl implements IAppUserService 
{
    @Autowired
    private AppUserMapper appUserMapper;

    /**
     * 查询APP用户
     * 
     * @param userId APP用户主键
     * @return APP用户
     */
    @Override
    public AppUser selectAppUserByUserId(Long userId)
    {
        return appUserMapper.selectAppUserByUserId(userId);
    }

    /**
     * 根据用户名查询APP用户
     * 
     * @param userName 用户名
     * @return APP用户
     */
    @Override
    public AppUser selectAppUserByUserName(String userName)
    {
        return appUserMapper.selectAppUserByUserName(userName);
    }

    /**
     * 查询APP用户列表
     * 
     * @param appUser APP用户
     * @return APP用户
     */
    @Override
    public List<AppUser> selectAppUserList(AppUser appUser)
    {
        return appUserMapper.selectAppUserList(appUser);
    }

    /**
     * 新增APP用户
     * 
     * @param appUser APP用户
     * @return 结果
     */
    @Override
    public int insertAppUser(AppUser appUser)
    {
        appUser.setCreateTime(DateUtils.getNowDate());
        return appUserMapper.insertAppUser(appUser);
    }

    /**
     * 修改APP用户
     * 
     * @param appUser APP用户
     * @return 结果
     */
    @Override
    public int updateAppUser(AppUser appUser)
    {
        appUser.setUpdateTime(DateUtils.getNowDate());
        return appUserMapper.updateAppUser(appUser);
    }

    /**
     * 批量删除APP用户
     * 
     * @param userIds 需要删除的APP用户主键
     * @return 结果
     */
    @Override
    public int deleteAppUserByUserIds(Long[] userIds)
    {
        return appUserMapper.deleteAppUserByUserIds(userIds);
    }

    /**
     * 删除APP用户信息
     * 
     * @param userId APP用户主键
     * @return 结果
     */
    @Override
    public int deleteAppUserByUserId(Long userId)
    {
        return appUserMapper.deleteAppUserByUserId(userId);
    }

    /**
     * 校验用户名称是否唯一
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(AppUser appUser)
    {
        Long userId = StringUtils.isNull(appUser.getUserId()) ? -1L : appUser.getUserId();
        AppUser info = appUserMapper.checkUserNameUnique(appUser.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验手机号码是否唯一
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkPhoneUnique(AppUser appUser)
    {
        Long userId = StringUtils.isNull(appUser.getUserId()) ? -1L : appUser.getUserId();
        AppUser info = appUserMapper.checkPhoneUnique(appUser.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验email是否唯一
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    @Override
    public boolean checkEmailUnique(AppUser appUser)
    {
        Long userId = StringUtils.isNull(appUser.getUserId()) ? -1L : appUser.getUserId();
        AppUser info = appUserMapper.checkEmailUnique(appUser.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 用户注册
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(AppUser appUser)
    {
        appUser.setCreateTime(DateUtils.getNowDate());
        appUser.setPassword(SecurityUtils.encryptPassword(appUser.getPassword()));
        return appUserMapper.insertAppUser(appUser) > 0;
    }

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public AppUser selectUserByUserName(String userName)
    {
        return appUserMapper.selectAppUserByUserName(userName);
    }

    /**
     * 通过手机号查询用户
     * 
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public AppUser selectUserByPhonenumber(String phonenumber)
    {
        return appUserMapper.selectAppUserByPhonenumber(phonenumber);
    }

    /**
     * 修改用户基本信息
     * 
     * @param appUser 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(AppUser appUser)
    {
        return appUserMapper.updateAppUser(appUser);
    }

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        AppUser appUser = new AppUser();
        appUser.setUserName(userName);
        appUser.setAvatar(avatar);
        return appUserMapper.updateAppUser(appUser) > 0;
    }

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password)
    {
        AppUser appUser = new AppUser();
        appUser.setUserName(userName);
        appUser.setPassword(SecurityUtils.encryptPassword(password));
        return appUserMapper.updateAppUser(appUser);
    }
}
