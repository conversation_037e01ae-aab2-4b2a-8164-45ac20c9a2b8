package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppConfig;

/**
 * APP配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface AppConfigMapper 
{
    /**
     * 查询APP配置
     * 
     * @param configId APP配置主键
     * @return APP配置
     */
    public AppConfig selectAppConfigByConfigId(Long configId);

    /**
     * 根据配置键查询配置
     * 
     * @param configKey 配置键
     * @return APP配置
     */
    public AppConfig selectAppConfigByKey(String configKey);

    /**
     * 查询APP配置列表
     * 
     * @param appConfig APP配置
     * @return APP配置集合
     */
    public List<AppConfig> selectAppConfigList(AppConfig appConfig);

    /**
     * 新增APP配置
     * 
     * @param appConfig APP配置
     * @return 结果
     */
    public int insertAppConfig(AppConfig appConfig);

    /**
     * 修改APP配置
     * 
     * @param appConfig APP配置
     * @return 结果
     */
    public int updateAppConfig(AppConfig appConfig);

    /**
     * 删除APP配置
     * 
     * @param configId APP配置主键
     * @return 结果
     */
    public int deleteAppConfigByConfigId(Long configId);

    /**
     * 批量删除APP配置
     * 
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppConfigByConfigIds(Long[] configIds);

    /**
     * 校验配置键名是否唯一
     * 
     * @param configKey 配置键名
     * @return 结果
     */
    public AppConfig checkConfigKeyUnique(String configKey);
}
