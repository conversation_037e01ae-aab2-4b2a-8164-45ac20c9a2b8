package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.AppUser;

/**
 * APP用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface AppUserMapper 
{
    /**
     * 查询APP用户
     * 
     * @param userId APP用户主键
     * @return APP用户
     */
    public AppUser selectAppUserByUserId(Long userId);

    /**
     * 根据用户名查询APP用户
     * 
     * @param userName 用户名
     * @return APP用户
     */
    public AppUser selectAppUserByUserName(String userName);

    /**
     * 根据手机号查询APP用户
     * 
     * @param phonenumber 手机号
     * @return APP用户
     */
    public AppUser selectAppUserByPhonenumber(String phonenumber);

    /**
     * 查询APP用户列表
     * 
     * @param appUser APP用户
     * @return APP用户集合
     */
    public List<AppUser> selectAppUserList(AppUser appUser);

    /**
     * 新增APP用户
     * 
     * @param appUser APP用户
     * @return 结果
     */
    public int insertAppUser(AppUser appUser);

    /**
     * 修改APP用户
     * 
     * @param appUser APP用户
     * @return 结果
     */
    public int updateAppUser(AppUser appUser);

    /**
     * 删除APP用户
     * 
     * @param userId APP用户主键
     * @return 结果
     */
    public int deleteAppUserByUserId(Long userId);

    /**
     * 批量删除APP用户
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppUserByUserIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     * 
     * @param userName 用户名称
     * @return 结果
     */
    public AppUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     * 
     * @param phonenumber 手机号码
     * @return 结果
     */
    public AppUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     * 
     * @param email 用户邮箱
     * @return 结果
     */
    public AppUser checkEmailUnique(String email);
}
