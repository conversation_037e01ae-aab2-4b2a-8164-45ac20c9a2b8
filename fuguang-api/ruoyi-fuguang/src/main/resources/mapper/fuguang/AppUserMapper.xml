<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppUserMapper">
    
    <resultMap type="AppUser" id="AppUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="userType"     column="user_type"    />
        <result property="authStatus"   column="auth_status"  />
        <result property="realName"     column="real_name"    />
        <result property="idCard"       column="id_card"      />
        <result property="address"      column="address"      />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"     column="latitude"     />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectAppUserVo">
        select user_id, user_name, nick_name, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, user_type, auth_status, real_name, id_card, address, longitude, latitude, create_by, create_time, update_by, update_time, remark from app_user
    </sql>

    <select id="selectAppUserList" parameterType="AppUser" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="authStatus != null  and authStatus != ''"> and auth_status = #{authStatus}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
        </where>
        and del_flag = '0'
        order by create_time desc
    </select>
    
    <select id="selectAppUserByUserId" parameterType="Long" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where user_id = #{userId} and del_flag = '0'
    </select>

    <select id="selectAppUserByUserName" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where user_name = #{userName} and del_flag = '0'
    </select>

    <select id="selectAppUserByPhonenumber" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where phonenumber = #{phonenumber} and del_flag = '0'
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where user_name = #{userName} and del_flag = '0' limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where phonenumber = #{phonenumber} and del_flag = '0' limit 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where email = #{email} and del_flag = '0' limit 1
    </select>
        
    <insert id="insertAppUser" parameterType="AppUser" useGeneratedKeys="true" keyProperty="userId">
        insert into app_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="email != null">email,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="sex != null">sex,</if>
            <if test="avatar != null">avatar,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="userType != null">user_type,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="realName != null">real_name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="address != null">address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="email != null">#{email},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="sex != null">#{sex},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="userType != null">#{userType},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="realName != null">#{realName},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="address != null">#{address},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppUser" parameterType="AppUser">
        update app_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteAppUserByUserId" parameterType="Long">
        update app_user set del_flag = '2' where user_id = #{userId}
    </delete>

    <delete id="deleteAppUserByUserIds" parameterType="String">
        update app_user set del_flag = '2' where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>
