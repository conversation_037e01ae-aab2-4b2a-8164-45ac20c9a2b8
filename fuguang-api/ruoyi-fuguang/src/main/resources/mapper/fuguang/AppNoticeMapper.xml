<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppNoticeMapper">
    
    <resultMap type="AppNotice" id="AppNoticeResult">
        <id     property="noticeId"      column="notice_id"      />
        <result property="noticeTitle"   column="notice_title"   />
        <result property="noticeContent" column="notice_content" />
        <result property="noticeType"    column="notice_type"    />
        <result property="noticeStatus"  column="notice_status"  />
        <result property="targetType"    column="target_type"    />
        <result property="targetUserId"  column="target_user_id" />
        <result property="isRead"        column="is_read"        />
        <result property="publishTime"   column="publish_time"   />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
    </resultMap>

    <sql id="selectAppNoticeVo">
        select notice_id, notice_title, notice_content, notice_type, notice_status, target_type, target_user_id, is_read, publish_time, create_by, create_time, update_by, update_time, remark from app_notice
    </sql>

    <select id="selectAppNoticeList" parameterType="AppNotice" resultMap="AppNoticeResult">
        <include refid="selectAppNoticeVo"/>
        <where>  
            <if test="noticeTitle != null  and noticeTitle != ''"> and notice_title like concat('%', #{noticeTitle}, '%')</if>
            <if test="noticeType != null  and noticeType != ''"> and notice_type = #{noticeType}</if>
            <if test="noticeStatus != null  and noticeStatus != ''"> and notice_status = #{noticeStatus}</if>
            <if test="targetType != null  and targetType != ''"> and target_type = #{targetType}</if>
            <if test="targetUserId != null "> and target_user_id = #{targetUserId}</if>
            <if test="isRead != null  and isRead != ''"> and is_read = #{isRead}</if>
        </where>
        order by publish_time desc
    </select>
    
    <select id="selectAppNoticeByNoticeId" parameterType="Long" resultMap="AppNoticeResult">
        <include refid="selectAppNoticeVo"/>
        where notice_id = #{noticeId}
    </select>
    
    <select id="selectNoticesByUser" resultMap="AppNoticeResult">
        <include refid="selectAppNoticeVo"/>
        where (target_type = '0' or (target_type = '1' and target_user_id = #{userId}))
        and notice_status = '0'
        order by publish_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <select id="selectLatestSystemNotices" resultMap="AppNoticeResult">
        <include refid="selectAppNoticeVo"/>
        where notice_type = '0' and notice_status = '0' and target_type = '0'
        order by publish_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <select id="countUnreadNotices" parameterType="Long" resultType="int">
        select count(*) from app_notice 
        where (target_type = '0' or (target_type = '1' and target_user_id = #{userId}))
        and notice_status = '0' and is_read = '0'
    </select>
        
    <insert id="insertAppNotice" parameterType="AppNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into app_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">notice_title,</if>
            <if test="noticeContent != null">notice_content,</if>
            <if test="noticeType != null">notice_type,</if>
            <if test="noticeStatus != null">notice_status,</if>
            <if test="targetType != null">target_type,</if>
            <if test="targetUserId != null">target_user_id,</if>
            <if test="isRead != null">is_read,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle},</if>
            <if test="noticeContent != null">#{noticeContent},</if>
            <if test="noticeType != null">#{noticeType},</if>
            <if test="noticeStatus != null">#{noticeStatus},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="targetUserId != null">#{targetUserId},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppNotice" parameterType="AppNotice">
        update app_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="noticeStatus != null">notice_status = #{noticeStatus},</if>
            <if test="targetType != null">target_type = #{targetType},</if>
            <if test="targetUserId != null">target_user_id = #{targetUserId},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteAppNoticeByNoticeId" parameterType="Long">
        delete from app_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteAppNoticeByNoticeIds" parameterType="String">
        delete from app_notice where notice_id in 
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>
    
    <update id="markNoticeAsRead">
        update app_notice set is_read = '1' 
        where notice_id = #{noticeId} 
        and (target_type = '0' or (target_type = '1' and target_user_id = #{userId}))
    </update>

</mapper>
