# 浮光登录页面实现说明

## 功能概述

根据您的需求，我已经重新设计并实现了浮光 APP 的登录页面，包含以下核心功能：

## 主要功能

### 1. 页面标题和说明

- **顶部标题**：显示"欢迎使用浮光"
- **副标题**：显示"未注册的手机号登录后会自动创建账号"

### 2. 登录方式切换

- **密码登录**：传统的手机号+密码登录方式
- **验证码登录**：手机号+短信验证码登录方式
- 两种方式可以通过顶部标签页进行切换

### 3. 输入表单

- **手机号输入**：支持 11 位手机号输入，带格式验证
- **密码输入**：密码登录模式下显示
- **验证码输入**：验证码登录模式下显示，包含获取验证码按钮和 60 秒倒计时

### 4. 协议同意机制

- **协议勾选**：用户需要勾选同意服务协议和隐私协议
- **协议内容**：点击协议链接可查看具体内容（从后台接口获取）
- **隐私保护弹框**：勾选协议时弹出详细的隐私保护说明

### 5. 游客模式

- **游客入口**：提供"游客模式，继续使用"选项
- **功能限制提示**：进入游客模式前会提示功能受限

## 技术实现

### API 接口扩展

在 `api/auth.js` 中新增了以下接口：

```javascript
// 发送验证码
export const sendSmsCode = (data) => {
  return post("/app/sms/send", data);
};

// 验证码登录
export const smsLogin = (data) => {
  return post("/app/sms/login", data);
};

// 获取用户协议（公共接口，无需登录）
export const getUserAgreement = () => {
  return publicGet("/app/agreement/user");
};

// 获取隐私政策（公共接口，无需登录）
export const getPrivacyPolicy = () => {
  return publicGet("/app/agreement/privacy");
};
```

### 请求工具扩展

在 `utils/request.js` 中新增了公共请求方法：

```javascript
// 公共GET请求（不需要token）
export const publicGet = (url, data = {}) => {
  return publicRequest({
    url,
    method: "GET",
    data,
  });
};
```

这样协议接口就不需要用户登录即可访问，确保在登录页面就能正常获取协议内容。

### 核心功能实现

#### 1. 登录方式切换

```javascript
switchLoginType(type) {
  this.loginType = type
  // 清空表单
  this.form.password = ''
  this.form.smsCode = ''
}
```

#### 2. 验证码发送和倒计时

```javascript
async sendSmsCode() {
  // 手机号验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(this.form.phone)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }

  // 发送验证码
  await sendSmsCode({ phone: this.form.phone })
  this.startCountdown()
}
```

#### 3. 隐私保护弹框

```javascript
showPrivacyModal() {
  uni.showModal({
    title: '隐私保护提示',
    content: '感谢您使用浮光APP，我们非常重视您的个人信息和隐私保护...',
    confirmText: '同意并继续',
    cancelText: '不同意，继续使用游客模式',
    success: (res) => {
      if (res.confirm) {
        this.agreed = true
      } else {
        this.agreed = false
        this.guestMode()
      }
    }
  })
}
```

#### 4. 统一登录处理

```javascript
async handleLogin() {
  let res
  if (this.loginType === 'password') {
    res = await login({
      phone: this.form.phone,
      password: this.form.password
    })
  } else {
    res = await smsLogin({
      phone: this.form.phone,
      smsCode: this.form.smsCode
    })
  }

  // 保存token和用户信息
  uni.setStorageSync('token', res.token)
  uni.removeStorageSync('isGuest') // 清除游客标识
}
```

## 用户体验优化

### 1. 视觉设计

- 保持原有的渐变背景和浮动装饰元素
- 新增登录方式切换标签页，采用现代化的设计风格
- 验证码按钮集成在输入框内，节省空间

### 2. 交互体验

- 登录方式切换时自动清空相关表单字段
- 验证码发送后显示 60 秒倒计时
- 协议勾选时立即弹出隐私保护说明
- 表单验证实时反馈

### 3. 错误处理

- 手机号格式验证
- 必填字段检查
- 网络请求异常处理
- 用户友好的错误提示

## 预览页面

我创建了一个 HTML 预览页面 `login-preview-new.html`，您可以在浏览器中查看新登录页面的效果。预览页面完整模拟了所有交互功能，包括：

- 登录方式切换
- 验证码发送和倒计时
- 协议同意和隐私保护弹框
- 游客模式确认
- 表单验证

## 后端接口需求

为了完整实现这些功能，后端需要提供以下接口：

1. **POST /app/sms/send** - 发送短信验证码
2. **POST /app/sms/login** - 验证码登录
3. **GET /app/agreement/user** - 获取用户协议内容
4. **GET /app/agreement/privacy** - 获取隐私政策内容

## 部署说明

1. 确保后端 API 接口已实现
2. 在管理后台配置服务协议和隐私协议内容
3. 测试短信验证码发送功能
4. 验证游客模式和正常登录的权限控制

这个实现完全符合您的需求，提供了现代化的用户体验，同时兼顾了隐私保护和游客模式的需求。
