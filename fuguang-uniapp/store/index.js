import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    // 用户信息
    userInfo: {},
    // 登录状态
    isLoggedIn: false,
    // 位置信息
    location: {
      longitude: "",
      latitude: "",
      address: "",
    },
    // 未读消息数
    unreadCount: 0,
    // 系统配置
    appConfig: {},
    // 网络状态
    networkStatus: true,
  },

  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      state.isLoggedIn = !!userInfo.userId;
    },

    // 清除用户信息
    CLEAR_USER_INFO(state) {
      state.userInfo = {};
      state.isLoggedIn = false;
    },

    // 设置位置信息
    SET_LOCATION(state, location) {
      state.location = location;
    },

    // 设置未读消息数
    SET_UNREAD_COUNT(state, count) {
      state.unreadCount = count;
    },

    // 设置APP配置
    SET_APP_CONFIG(state, config) {
      state.appConfig = config;
    },

    // 设置网络状态
    SET_NETWORK_STATUS(state, status) {
      state.networkStatus = status;
    },
  },

  actions: {
    // 登录
    login({ commit }, userInfo) {
      commit("SET_USER_INFO", userInfo);
      uni.setStorageSync("userInfo", userInfo);
    },

    // 退出登录
    logout({ commit }) {
      commit("CLEAR_USER_INFO");
      uni.removeStorageSync("token");
      uni.removeStorageSync("userInfo");
    },

    // 更新用户信息
    updateUserInfo({ commit, state }, userInfo) {
      const newUserInfo = { ...state.userInfo, ...userInfo };
      commit("SET_USER_INFO", newUserInfo);
      uni.setStorageSync("userInfo", newUserInfo);
    },

    // 更新位置信息
    updateLocation({ commit }, location) {
      commit("SET_LOCATION", location);
    },

    // 更新未读消息数
    updateUnreadCount({ commit }, count) {
      commit("SET_UNREAD_COUNT", count);
    },

    // 初始化应用状态
    initApp({ commit }) {
      // 从本地存储恢复用户信息
      const userInfo = uni.getStorageSync("userInfo");
      if (userInfo) {
        commit("SET_USER_INFO", userInfo);
      }

      // 监听网络状态
      uni.onNetworkStatusChange((res) => {
        commit("SET_NETWORK_STATUS", res.isConnected);
      });
    },
  },

  getters: {
    // 是否已登录
    isLoggedIn: (state) => state.isLoggedIn,

    // 用户ID
    userId: (state) => state.userInfo.userId,

    // 用户昵称
    nickName: (state) => state.userInfo.nickName,

    // 是否实名认证
    isAuthenticated: (state) => state.userInfo.authStatus === "1",

    // 当前位置
    currentLocation: (state) => state.location.address || "未知位置",

    // 是否有未读消息
    hasUnreadMessage: (state) => state.unreadCount > 0,
  },
});

export default store;
