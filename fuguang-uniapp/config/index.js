// 应用配置
const config = {
  // 开发环境配置
  development: {
    baseURL: 'http://localhost:8888',
    timeout: 10000,
    debug: true
  },
  
  // 生产环境配置
  production: {
    baseURL: 'https://api.fuguang.com',
    timeout: 10000,
    debug: false
  },
  
  // 测试环境配置
  test: {
    baseURL: 'https://test-api.fuguang.com',
    timeout: 10000,
    debug: true
  }
}

// 获取当前环境
const getEnv = () => {
  // #ifdef H5
  return process.env.NODE_ENV || 'development'
  // #endif
  
  // #ifndef H5
  return 'development' // 小程序默认开发环境
  // #endif
}

// 导出当前环境配置
export default config[getEnv()]
