// 任务相关API
import { get, post, put } from '@/utils/request'

// 获取任务列表
export const getTaskList = (params) => {
  return get('/app/task/list', params)
}

// 获取任务详情
export const getTaskDetail = (taskId) => {
  return get(`/app/task/${taskId}`)
}

// 发布任务
export const publishTask = (data) => {
  return post('/app/task', data)
}

// 修改任务
export const updateTask = (data) => {
  return put('/app/task', data)
}

// 接取任务
export const acceptTask = (taskId) => {
  return post(`/app/task/accept/${taskId}`)
}

// 完成任务
export const completeTask = (taskId) => {
  return post(`/app/task/complete/${taskId}`)
}

// 取消任务
export const cancelTask = (taskId) => {
  return post(`/app/task/cancel/${taskId}`)
}

// 获取我发布的任务
export const getMyPublishedTasks = () => {
  return get('/app/task/my-published')
}

// 获取我接取的任务
export const getMyReceivedTasks = () => {
  return get('/app/task/my-received')
}

// 获取热门任务
export const getHotTasks = (params) => {
  return get('/app/task/hot', params)
}
