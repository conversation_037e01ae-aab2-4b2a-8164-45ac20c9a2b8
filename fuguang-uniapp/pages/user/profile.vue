<template>
  <view class="profile-container">
    <scroll-view class="profile-scroll" scroll-y>
      <!-- 头像 -->
      <view class="avatar-section">
        <view class="avatar-item" @click="chooseAvatar">
          <text class="label">头像</text>
          <view class="avatar-right">
            <image class="avatar" :src="form.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="form-item" @click="editNickname">
          <text class="label">昵称</text>
          <view class="value-right">
            <text class="value">{{ form.nickName || '未设置' }}</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
        
        <view class="form-item">
          <text class="label">用户名</text>
          <text class="value">{{ form.userName }}</text>
        </view>
        
        <view class="form-item" @click="editPhone">
          <text class="label">手机号</text>
          <view class="value-right">
            <text class="value">{{ form.phonenumber || '未绑定' }}</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
        
        <view class="form-item" @click="editEmail">
          <text class="label">邮箱</text>
          <view class="value-right">
            <text class="value">{{ form.email || '未绑定' }}</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
        
        <view class="form-item" @click="selectSex">
          <text class="label">性别</text>
          <view class="value-right">
            <text class="value">{{ getSexText(form.sex) }}</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 地址信息 -->
      <view class="form-section">
        <view class="form-item" @click="chooseLocation">
          <text class="label">常用地址</text>
          <view class="value-right">
            <text class="value">{{ form.address || '未设置' }}</text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 安全设置 -->
      <view class="form-section">
        <view class="form-item" @click="changePassword">
          <text class="label">修改密码</text>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
        
        <view class="form-item" @click="goAuth">
          <text class="label">实名认证</text>
          <view class="value-right">
            <text class="value" :class="{ verified: form.authStatus === '1' }">
              {{ form.authStatus === '1' ? '已认证' : '未认证' }}
            </text>
            <u-icon name="arrow-right" size="24" color="#999"></u-icon>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 编辑弹窗 -->
    <u-modal v-model="showEditModal" :title="editTitle" @confirm="confirmEdit" @cancel="cancelEdit">
      <view class="edit-content">
        <u-input 
          v-model="editValue" 
          :placeholder="editPlaceholder"
          :maxlength="editMaxLength"
          :clearable="true"
        />
      </view>
    </u-modal>
    
    <!-- 性别选择 -->
    <u-action-sheet 
      v-model="showSexSheet" 
      :list="sexOptions" 
      @click="selectSexOption"
    ></u-action-sheet>
  </view>
</template>

<script>
import { getUserInfo, updateUserInfo, uploadAvatar } from '@/api/auth'
import { validatePhone, validateEmail, chooseImage } from '@/utils/common'

export default {
  data() {
    return {
      form: {},
      showEditModal: false,
      editTitle: '',
      editPlaceholder: '',
      editValue: '',
      editField: '',
      editMaxLength: 50,
      showSexSheet: false,
      sexOptions: [
        { text: '男', value: '0' },
        { text: '女', value: '1' },
        { text: '保密', value: '2' }
      ]
    }
  },
  
  onLoad() {
    this.loadUserInfo()
  },
  
  methods: {
    async loadUserInfo() {
      try {
        const res = await getUserInfo()
        this.form = res.data || {}
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },
    
    async chooseAvatar() {
      try {
        const images = await chooseImage(1)
        if (images.length > 0) {
          uni.showLoading({ title: '上传中...' })
          
          const res = await uploadAvatar(images[0])
          this.form.avatar = res.data.imgUrl
          
          uni.hideLoading()
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('上传头像失败:', error)
      }
    },
    
    editNickname() {
      this.editTitle = '修改昵称'
      this.editPlaceholder = '请输入昵称'
      this.editValue = this.form.nickName || ''
      this.editField = 'nickName'
      this.editMaxLength = 20
      this.showEditModal = true
    },
    
    editPhone() {
      this.editTitle = '修改手机号'
      this.editPlaceholder = '请输入手机号'
      this.editValue = this.form.phonenumber || ''
      this.editField = 'phonenumber'
      this.editMaxLength = 11
      this.showEditModal = true
    },
    
    editEmail() {
      this.editTitle = '修改邮箱'
      this.editPlaceholder = '请输入邮箱'
      this.editValue = this.form.email || ''
      this.editField = 'email'
      this.editMaxLength = 50
      this.showEditModal = true
    },
    
    selectSex() {
      this.showSexSheet = true
    },
    
    selectSexOption(index) {
      this.form.sex = this.sexOptions[index].value
      this.updateProfile({ sex: this.form.sex })
    },
    
    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.form.address = res.name || res.address
          this.updateProfile({
            address: this.form.address,
            longitude: res.longitude.toString(),
            latitude: res.latitude.toString()
          })
        }
      })
    },
    
    async confirmEdit() {
      if (!this.editValue.trim()) {
        uni.showToast({
          title: '请输入内容',
          icon: 'none'
        })
        return
      }
      
      // 验证手机号
      if (this.editField === 'phonenumber' && !validatePhone(this.editValue)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }
      
      // 验证邮箱
      if (this.editField === 'email' && !validateEmail(this.editValue)) {
        uni.showToast({
          title: '邮箱格式不正确',
          icon: 'none'
        })
        return
      }
      
      this.form[this.editField] = this.editValue
      await this.updateProfile({ [this.editField]: this.editValue })
      this.showEditModal = false
    },
    
    cancelEdit() {
      this.showEditModal = false
      this.editValue = ''
    },
    
    async updateProfile(data) {
      try {
        await updateUserInfo(data)
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('更新用户信息失败:', error)
      }
    },
    
    changePassword() {
      uni.navigateTo({
        url: '/pages/user/password'
      })
    },
    
    goAuth() {
      uni.navigateTo({
        url: '/pages/user/auth'
      })
    },
    
    getSexText(sex) {
      const sexMap = {
        '0': '男',
        '1': '女',
        '2': '保密'
      }
      return sexMap[sex] || '未设置'
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  height: 100vh;
  background: #f8f8f8;
}

.profile-scroll {
  height: 100%;
  padding: 40rpx;
}

.avatar-section, .form-section {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.avatar-item, .form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    font-size: 30rpx;
    color: #333;
  }
  
  .value {
    font-size: 30rpx;
    color: #666;
    
    &.verified {
      color: #4caf50;
    }
  }
  
  .value-right {
    display: flex;
    align-items: center;
    gap: 20rpx;
  }
}

.avatar-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
  }
}

.edit-content {
  padding: 40rpx 0;
}
</style>
