<template>
  <view class="user-center-container">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info" @click="goProfile">
        <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
        <view class="user-detail">
          <text class="nickname">{{ userInfo.nickName || '未登录' }}</text>
          <text class="username">{{ userInfo.userName || '点击登录' }}</text>
          <view class="auth-status" v-if="userInfo.authStatus">
            <text class="auth-text" :class="{ verified: userInfo.authStatus === '1' }">
              {{ userInfo.authStatus === '1' ? '已实名认证' : '未实名认证' }}
            </text>
          </view>
        </view>
        <u-icon name="arrow-right" size="32" color="#999"></u-icon>
      </view>
      
      <!-- 统计信息 -->
      <view class="stats-row" v-if="isLoggedIn">
        <view class="stat-item" @click="goMyTasks('published')">
          <text class="stat-number">{{ stats.publishedCount || 0 }}</text>
          <text class="stat-label">发布任务</text>
        </view>
        <view class="stat-item" @click="goMyTasks('received')">
          <text class="stat-number">{{ stats.receivedCount || 0 }}</text>
          <text class="stat-label">接取任务</text>
        </view>
        <view class="stat-item" @click="goWallet">
          <text class="stat-number">¥{{ formatMoney(stats.balance || 0) }}</text>
          <text class="stat-label">账户余额</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="goMyTasks()">
          <view class="menu-left">
            <u-icon name="list" size="40" color="#3cc51f"></u-icon>
            <text class="menu-text">我的任务</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
        
        <view class="menu-item" @click="goWallet">
          <view class="menu-left">
            <u-icon name="wallet" size="40" color="#ff9800"></u-icon>
            <text class="menu-text">我的钱包</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
        
        <view class="menu-item" @click="goAuth">
          <view class="menu-left">
            <u-icon name="account" size="40" color="#2196f3"></u-icon>
            <text class="menu-text">实名认证</text>
            <text class="auth-badge" v-if="userInfo.authStatus === '0'">未认证</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="goNotices">
          <view class="menu-left">
            <u-icon name="bell" size="40" color="#ff6b35"></u-icon>
            <text class="menu-text">消息通知</text>
            <view class="badge" v-if="unreadCount > 0">{{ unreadCount > 99 ? '99+' : unreadCount }}</view>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
        
        <view class="menu-item" @click="goService">
          <view class="menu-left">
            <u-icon name="customer-service" size="40" color="#9c27b0"></u-icon>
            <text class="menu-text">客服中心</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
        
        <view class="menu-item" @click="goSettings">
          <view class="menu-left">
            <u-icon name="setting" size="40" color="#607d8b"></u-icon>
            <text class="menu-text">设置</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
      </view>
      
      <view class="menu-group">
        <view class="menu-item" @click="goAbout">
          <view class="menu-left">
            <u-icon name="info-circle" size="40" color="#795548"></u-icon>
            <text class="menu-text">关于我们</text>
          </view>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section" v-if="isLoggedIn">
      <u-button type="error" size="large" @click="logout">退出登录</u-button>
    </view>
  </view>
</template>

<script>
import { getUserInfo } from '@/api/auth'
import { getUnreadNoticeCount } from '@/api/home'
import { formatMoney } from '@/utils/common'

export default {
  data() {
    return {
      userInfo: {},
      stats: {},
      unreadCount: 0
    }
  },
  
  computed: {
    isLoggedIn() {
      return !!uni.getStorageSync('token')
    }
  },
  
  onShow() {
    if (this.isLoggedIn) {
      this.loadUserInfo()
      this.loadUnreadCount()
    } else {
      this.userInfo = {}
      this.stats = {}
      this.unreadCount = 0
    }
  },
  
  methods: {
    async loadUserInfo() {
      try {
        const res = await getUserInfo()
        this.userInfo = res.data || {}
        
        // 模拟统计数据，实际应该从后端获取
        this.stats = {
          publishedCount: 5,
          receivedCount: 8,
          balance: 128.50
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },
    
    async loadUnreadCount() {
      try {
        const res = await getUnreadNoticeCount()
        this.unreadCount = res.data || 0
      } catch (error) {
        console.error('加载未读消息数失败:', error)
      }
    },
    
    goProfile() {
      if (!this.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        })
      } else {
        uni.navigateTo({
          url: '/pages/user/profile'
        })
      }
    },
    
    goMyTasks(tab = 'published') {
      if (!this.isLoggedIn) {
        this.showLoginTip()
        return
      }
      
      uni.navigateTo({
        url: `/pages/task/my?tab=${tab}`
      })
    },
    
    goWallet() {
      if (!this.isLoggedIn) {
        this.showLoginTip()
        return
      }
      
      uni.navigateTo({
        url: '/pages/wallet/index'
      })
    },
    
    goAuth() {
      if (!this.isLoggedIn) {
        this.showLoginTip()
        return
      }
      
      uni.navigateTo({
        url: '/pages/user/auth'
      })
    },
    
    goNotices() {
      uni.switchTab({
        url: '/pages/notice/list'
      })
    },
    
    goService() {
      uni.navigateTo({
        url: '/pages/service/index'
      })
    },
    
    goSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      })
    },
    
    goAbout() {
      uni.navigateTo({
        url: '/pages/about/index'
      })
    },
    
    showLoginTip() {
      uni.showModal({
        title: '提示',
        content: '请先登录',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
    },
    
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')
            
            this.userInfo = {}
            this.stats = {}
            this.unreadCount = 0
            
            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    },
    
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.user-center-container {
  min-height: 100vh;
  background: #f8f8f8;
}

.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
      margin-right: 30rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
    }
    
    .user-detail {
      flex: 1;
      
      .nickname {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 10rpx;
      }
      
      .username {
        display: block;
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 10rpx;
      }
      
      .auth-status {
        .auth-text {
          font-size: 24rpx;
          padding: 8rpx 16rpx;
          border-radius: 16rpx;
          background: rgba(255, 255, 255, 0.2);
          color: #ffffff;
          
          &.verified {
            background: rgba(76, 175, 80, 0.8);
          }
        }
      }
    }
  }
  
  .stats-row {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 30rpx 0;
    
    .stat-item {
      flex: 1;
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 10rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.menu-section {
  padding: 40rpx;
}

.menu-group {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  
  .menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .menu-left {
      display: flex;
      align-items: center;
      position: relative;
      
      .menu-text {
        margin-left: 20rpx;
        font-size: 30rpx;
        color: #333;
      }
      
      .auth-badge {
        margin-left: 20rpx;
        font-size: 22rpx;
        color: #ff6b35;
        background: #fff3e0;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
      }
      
      .badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        background: #ff4757;
        color: #ffffff;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 20rpx;
        min-width: 32rpx;
        text-align: center;
      }
    }
  }
}

.logout-section {
  padding: 0 40rpx 40rpx;
}
</style>
