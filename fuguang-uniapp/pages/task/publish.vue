<template>
  <view class="publish-container">
    <scroll-view class="form-scroll" scroll-y>
      <view class="form-container">
        <!-- 任务标题 -->
        <view class="form-item">
          <text class="label">任务标题 *</text>
          <u-input 
            v-model="form.taskTitle" 
            placeholder="请输入任务标题"
            maxlength="50"
            :clearable="true"
          />
          <text class="char-count">{{ form.taskTitle.length }}/50</text>
        </view>
        
        <!-- 任务描述 -->
        <view class="form-item">
          <text class="label">任务描述 *</text>
          <u-textarea 
            v-model="form.taskDesc" 
            placeholder="请详细描述任务内容、要求等"
            maxlength="500"
            height="200"
          />
          <text class="char-count">{{ form.taskDesc.length }}/500</text>
        </view>
        
        <!-- 任务金额 -->
        <view class="form-item">
          <text class="label">任务金额 *</text>
          <view class="amount-input">
            <text class="currency">¥</text>
            <u-input 
              v-model="form.taskAmount" 
              type="number"
              placeholder="0.00"
              :clearable="true"
            />
          </view>
          <text class="tip">建议设置合理的金额以吸引更多人接取</text>
        </view>
        
        <!-- 任务地址 -->
        <view class="form-item">
          <text class="label">任务地址 *</text>
          <view class="location-input" @click="chooseLocation">
            <u-input 
              v-model="form.taskAddress" 
              placeholder="点击选择任务地址"
              :disabled="true"
              suffix-icon="map"
            />
          </view>
        </view>
        
        <!-- 任务类型 -->
        <view class="form-item">
          <text class="label">任务类型</text>
          <u-radio-group v-model="form.taskType" placement="row">
            <u-radio label="0" name="普通任务">普通任务</u-radio>
            <u-radio label="1" name="紧急任务">紧急任务</u-radio>
          </u-radio-group>
          <text class="tip" v-if="form.taskType === '1'">紧急任务会优先展示，建议提高金额</text>
        </view>
        
        <!-- 时间设置 -->
        <view class="form-item">
          <text class="label">开始时间</text>
          <u-datetime-picker 
            v-model="form.startTime"
            mode="datetime"
            :min-date="minDate"
            @confirm="onStartTimeConfirm"
          >
            <template #default>
              <view class="time-input">
                <text class="time-text">{{ startTimeText || '请选择开始时间' }}</text>
                <u-icon name="arrow-right" size="24" color="#999"></u-icon>
              </view>
            </template>
          </u-datetime-picker>
        </view>
        
        <view class="form-item">
          <text class="label">结束时间</text>
          <u-datetime-picker 
            v-model="form.endTime"
            mode="datetime"
            :min-date="form.startTime || minDate"
            @confirm="onEndTimeConfirm"
          >
            <template #default>
              <view class="time-input">
                <text class="time-text">{{ endTimeText || '请选择结束时间' }}</text>
                <u-icon name="arrow-right" size="24" color="#999"></u-icon>
              </view>
            </template>
          </u-datetime-picker>
        </view>
        
        <!-- 温馨提示 -->
        <view class="tips-card">
          <view class="tips-title">温馨提示</view>
          <view class="tips-content">
            <text class="tip-item">• 请确保任务描述真实详细</text>
            <text class="tip-item">• 任务金额一经发布不可修改</text>
            <text class="tip-item">• 恶意发布虚假任务将被封号</text>
            <text class="tip-item">• 建议合理设置任务时间</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部发布按钮 -->
    <view class="publish-bar">
      <u-button 
        type="primary" 
        size="large"
        :loading="publishing"
        :disabled="!canPublish"
        @click="handlePublish"
      >
        {{ isEdit ? '保存修改' : '发布任务' }}
      </u-button>
    </view>
  </view>
</template>

<script>
import { publishTask, updateTask, getTaskDetail } from '@/api/task'
import { checkLogin, getCurrentLocation } from '@/utils/common'

export default {
  data() {
    return {
      isEdit: false,
      taskId: '',
      form: {
        taskTitle: '',
        taskDesc: '',
        taskAmount: '',
        taskAddress: '',
        taskType: '0',
        startTime: '',
        endTime: '',
        longitude: '',
        latitude: ''
      },
      publishing: false,
      minDate: new Date().getTime()
    }
  },
  
  computed: {
    canPublish() {
      return this.form.taskTitle && 
             this.form.taskDesc && 
             this.form.taskAmount && 
             this.form.taskAddress &&
             !this.publishing
    },
    
    startTimeText() {
      return this.form.startTime ? this.formatDateTime(this.form.startTime) : ''
    },
    
    endTimeText() {
      return this.form.endTime ? this.formatDateTime(this.form.endTime) : ''
    }
  },
  
  onLoad(options) {
    if (!checkLogin()) {
      uni.navigateBack()
      return
    }
    
    if (options.id) {
      this.isEdit = true
      this.taskId = options.id
      this.loadTaskDetail()
    } else {
      this.getCurrentLocation()
    }
  },
  
  methods: {
    async getCurrentLocation() {
      try {
        const location = await getCurrentLocation()
        this.form.longitude = location.longitude.toString()
        this.form.latitude = location.latitude.toString()
        this.form.taskAddress = location.address || ''
      } catch (error) {
        console.error('获取位置失败:', error)
      }
    },
    
    async loadTaskDetail() {
      try {
        const res = await getTaskDetail(this.taskId)
        const task = res.data
        
        this.form = {
          taskTitle: task.taskTitle,
          taskDesc: task.taskDesc,
          taskAmount: task.taskAmount.toString(),
          taskAddress: task.taskAddress,
          taskType: task.taskType,
          startTime: task.startTime,
          endTime: task.endTime,
          longitude: task.longitude,
          latitude: task.latitude
        }
      } catch (error) {
        console.error('加载任务详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.form.taskAddress = res.name || res.address
          this.form.longitude = res.longitude.toString()
          this.form.latitude = res.latitude.toString()
        },
        fail: (error) => {
          console.error('选择位置失败:', error)
        }
      })
    },
    
    onStartTimeConfirm(value) {
      this.form.startTime = value
    },
    
    onEndTimeConfirm(value) {
      this.form.endTime = value
    },
    
    async handlePublish() {
      if (!this.canPublish) return
      
      // 验证金额
      const amount = parseFloat(this.form.taskAmount)
      if (isNaN(amount) || amount <= 0) {
        uni.showToast({
          title: '请输入有效的金额',
          icon: 'none'
        })
        return
      }
      
      // 验证时间
      if (this.form.startTime && this.form.endTime) {
        if (new Date(this.form.endTime) <= new Date(this.form.startTime)) {
          uni.showToast({
            title: '结束时间必须晚于开始时间',
            icon: 'none'
          })
          return
        }
      }
      
      this.publishing = true
      try {
        const data = {
          ...this.form,
          taskAmount: amount
        }
        
        if (this.isEdit) {
          data.taskId = this.taskId
          await updateTask(data)
          uni.showToast({
            title: '修改成功',
            icon: 'success'
          })
        } else {
          await publishTask(data)
          uni.showToast({
            title: '发布成功',
            icon: 'success'
          })
        }
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        
      } catch (error) {
        console.error('发布任务失败:', error)
      } finally {
        this.publishing = false
      }
    },
    
    formatDateTime(timestamp) {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hour}:${minute}`
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.form-scroll {
  flex: 1;
}

.form-container {
  padding: 40rpx;
}

.form-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .label {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .char-count {
    display: block;
    text-align: right;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
  
  .tip {
    display: block;
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
}

.amount-input {
  display: flex;
  align-items: center;
  
  .currency {
    font-size: 36rpx;
    font-weight: bold;
    color: #ff6b35;
    margin-right: 10rpx;
  }
}

.location-input {
  .u-input {
    background: #f8f8f8;
  }
}

.time-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  .time-text {
    font-size: 30rpx;
    color: #333;
  }
}

.tips-card {
  background: #fff7f0;
  border-radius: 20rpx;
  padding: 30rpx;
  border-left: 8rpx solid #ff6b35;
  
  .tips-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 20rpx;
  }
  
  .tips-content {
    .tip-item {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 10rpx;
    }
  }
}

.publish-bar {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}
</style>
