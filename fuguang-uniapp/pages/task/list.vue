<template>
  <view class="task-list-container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" :class="{ active: currentFilter === 'all' }" @click="setFilter('all')">
        全部
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'nearby' }" @click="setFilter('nearby')">
        附近
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'urgent' }" @click="setFilter('urgent')">
        紧急
      </view>
      <view class="filter-item" :class="{ active: currentFilter === 'high-pay' }" @click="setFilter('high-pay')">
        高薪
      </view>
    </view>
    
    <!-- 任务列表 -->
    <scroll-view 
      class="task-scroll" 
      scroll-y 
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <view class="task-list">
        <view class="task-item" v-for="task in taskList" :key="task.taskId" @click="goTaskDetail(task.taskId)">
          <view class="task-header">
            <view class="user-info">
              <image class="avatar" :src="task.publisherAvatar || '/static/default-avatar.png'" mode="aspectFill"></image>
              <view class="user-detail">
                <text class="username">{{ task.publisherName }}</text>
                <text class="publish-info">{{ formatTime(task.createTime) }} · {{ task.taskAddress }}</text>
              </view>
            </view>
            <view class="task-status" :class="getStatusClass(task.taskStatus)">
              {{ getStatusText(task.taskStatus) }}
            </view>
          </view>
          
          <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
          <view class="task-title">{{ task.taskTitle }}</view>
          <view class="task-desc">{{ task.taskDesc }}</view>
          
          <view class="task-footer">
            <view class="task-meta">
              <text class="view-count">{{ task.viewCount || 0 }}次浏览</text>
              <text class="hot-score" v-if="task.hotScore > 0">🔥{{ task.hotScore }}</text>
            </view>
            <view class="task-type" v-if="task.taskType === '1'">
              <text class="urgent-tag">紧急</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <u-loading-icon v-if="loading"></u-loading-icon>
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && taskList.length > 0">
        <text>没有更多任务了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && taskList.length === 0">
        <image class="empty-image" src="/static/empty-task.png" mode="aspectFit"></image>
        <text class="empty-text">暂无任务</text>
        <u-button type="primary" size="small" @click="goPublish">发布任务</u-button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getTaskList } from '@/api/task'
import { formatTime, formatMoney, getCurrentLocation } from '@/utils/common'

export default {
  data() {
    return {
      currentFilter: 'all',
      taskList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      page: 1,
      pageSize: 10,
      longitude: '',
      latitude: ''
    }
  },
  
  onLoad() {
    this.getLocation()
    this.loadTaskList()
  },
  
  onShow() {
    // 从发布页面返回时刷新列表
    if (this.taskList.length > 0) {
      this.onRefresh()
    }
  },
  
  methods: {
    async getLocation() {
      try {
        const location = await getCurrentLocation()
        this.longitude = location.longitude
        this.latitude = location.latitude
      } catch (error) {
        console.error('获取位置失败:', error)
      }
    },
    
    setFilter(filter) {
      this.currentFilter = filter
      this.page = 1
      this.hasMore = true
      this.taskList = []
      this.loadTaskList()
    },
    
    async loadTaskList() {
      if (this.loading || !this.hasMore) return
      
      this.loading = true
      try {
        const params = {
          pageNum: this.page,
          pageSize: this.pageSize,
          longitude: this.longitude,
          latitude: this.latitude
        }
        
        // 根据筛选条件添加参数
        switch (this.currentFilter) {
          case 'nearby':
            // 附近任务需要位置信息
            break
          case 'urgent':
            params.taskType = '1'
            break
          case 'high-pay':
            params.orderBy = 'task_amount desc'
            break
        }
        
        const res = await getTaskList(params)
        const newTasks = res.rows || []
        
        if (this.page === 1) {
          this.taskList = newTasks
        } else {
          this.taskList.push(...newTasks)
        }
        
        this.hasMore = newTasks.length === this.pageSize
        this.page++
        
      } catch (error) {
        console.error('加载任务列表失败:', error)
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },
    
    loadMore() {
      this.loadTaskList()
    },
    
    onRefresh() {
      this.refreshing = true
      this.page = 1
      this.hasMore = true
      this.taskList = []
      this.loadTaskList()
    },
    
    goTaskDetail(taskId) {
      uni.navigateTo({
        url: `/pages/task/detail?id=${taskId}`
      })
    },
    
    goPublish() {
      uni.switchTab({
        url: '/pages/task/publish'
      })
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': '待接取',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'waiting',
        '1': 'processing',
        '2': 'completed',
        '3': 'cancelled'
      }
      return classMap[status] || ''
    },
    
    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.task-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.filter-bar {
  display: flex;
  background: #ffffff;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  .filter-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #666;
    border-radius: 40rpx;
    margin: 0 10rpx;
    
    &.active {
      background: #3cc51f;
      color: #ffffff;
    }
  }
}

.task-scroll {
  flex: 1;
}

.task-list {
  padding: 20rpx 40rpx;
  
  .task-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    
    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .user-info {
        display: flex;
        align-items: center;
        
        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          margin-right: 20rpx;
        }
        
        .user-detail {
          .username {
            display: block;
            font-size: 28rpx;
            color: #333;
            margin-bottom: 5rpx;
          }
          
          .publish-info {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
      
      .task-status {
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        
        &.waiting {
          background: #e8f5e8;
          color: #3cc51f;
        }
        
        &.processing {
          background: #fff3e0;
          color: #ff9800;
        }
        
        &.completed {
          background: #e3f2fd;
          color: #2196f3;
        }
        
        &.cancelled {
          background: #ffebee;
          color: #f44336;
        }
      }
    }
    
    .task-amount {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff6b35;
      margin-bottom: 10rpx;
    }
    
    .task-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .task-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 20rpx;
    }
    
    .task-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .task-meta {
        display: flex;
        gap: 20rpx;
        
        .view-count, .hot-score {
          font-size: 24rpx;
          color: #999;
        }
        
        .hot-score {
          color: #ff6b35;
        }
      }
      
      .urgent-tag {
        background: #ff4757;
        color: #ffffff;
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
      }
    }
  }
}

.load-more, .no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  
  .empty-image {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    display: block;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
}
</style>
