<template>
  <view class="task-detail-container" v-if="task">
    <scroll-view class="detail-scroll" scroll-y>
      <!-- 任务基本信息 -->
      <view class="task-info">
        <view class="task-header">
          <view class="user-info">
            <image class="avatar" :src="task.publisherAvatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <view class="user-detail">
              <text class="username">{{ task.publisherName }}</text>
              <text class="publish-info">{{ formatTime(task.createTime) }}</text>
            </view>
          </view>
          <view class="task-status" :class="getStatusClass(task.taskStatus)">
            {{ getStatusText(task.taskStatus) }}
          </view>
        </view>
        
        <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
        <view class="task-title">{{ task.taskTitle }}</view>
        <view class="task-desc">{{ task.taskDesc }}</view>
        
        <view class="task-meta">
          <view class="meta-item">
            <u-icon name="map" size="32" color="#666"></u-icon>
            <text class="meta-text">{{ task.taskAddress }}</text>
          </view>
          <view class="meta-item" v-if="task.startTime">
            <u-icon name="clock" size="32" color="#666"></u-icon>
            <text class="meta-text">开始时间：{{ formatTime(task.startTime) }}</text>
          </view>
          <view class="meta-item" v-if="task.endTime">
            <u-icon name="clock" size="32" color="#666"></u-icon>
            <text class="meta-text">结束时间：{{ formatTime(task.endTime) }}</text>
          </view>
          <view class="meta-item">
            <u-icon name="eye" size="32" color="#666"></u-icon>
            <text class="meta-text">{{ task.viewCount || 0 }}次浏览</text>
          </view>
        </view>
        
        <view class="task-type" v-if="task.taskType === '1'">
          <text class="urgent-tag">🔥 紧急任务</text>
        </view>
      </view>
      
      <!-- 接取者信息 -->
      <view class="receiver-info" v-if="task.receiverId && task.receiverName">
        <view class="section-title">接取者</view>
        <view class="receiver-detail">
          <text class="receiver-name">{{ task.receiverName }}</text>
          <text class="receive-time">接取时间：{{ formatTime(task.updateTime) }}</text>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="contact-info" v-if="showContact">
        <view class="section-title">联系方式</view>
        <view class="contact-item" @click="makeCall">
          <u-icon name="phone" size="32" color="#3cc51f"></u-icon>
          <text class="contact-text">拨打电话</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作栏 -->
    <view class="action-bar" v-if="showActionBar">
      <view class="action-buttons">
        <!-- 待接取状态 -->
        <template v-if="task.taskStatus === '0' && !isMyTask">
          <u-button type="primary" @click="acceptTask" :loading="actionLoading">
            接取任务
          </u-button>
        </template>
        
        <!-- 进行中状态 -->
        <template v-if="task.taskStatus === '1'">
          <u-button v-if="isReceiver" type="success" @click="completeTask" :loading="actionLoading">
            完成任务
          </u-button>
          <u-button v-if="isPublisher" type="warning" @click="cancelTask" :loading="actionLoading">
            取消任务
          </u-button>
        </template>
        
        <!-- 我发布的待接取任务 -->
        <template v-if="task.taskStatus === '0' && isMyTask">
          <u-button type="warning" @click="editTask">编辑任务</u-button>
          <u-button type="error" @click="cancelTask" :loading="actionLoading">取消任务</u-button>
        </template>
      </view>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" v-else>
    <u-loading-icon size="60"></u-loading-icon>
    <text class="loading-text">加载中...</text>
  </view>
</template>

<script>
import { getTaskDetail, acceptTask, completeTask, cancelTask } from '@/api/task'
import { formatTime, formatMoney, checkLogin } from '@/utils/common'

export default {
  data() {
    return {
      taskId: '',
      task: null,
      actionLoading: false,
      currentUserId: null
    }
  },
  
  computed: {
    isMyTask() {
      return this.task && this.currentUserId && this.task.publisherId === this.currentUserId
    },
    
    isPublisher() {
      return this.task && this.currentUserId && this.task.publisherId === this.currentUserId
    },
    
    isReceiver() {
      return this.task && this.currentUserId && this.task.receiverId === this.currentUserId
    },
    
    showContact() {
      return this.task && (this.isPublisher || this.isReceiver)
    },
    
    showActionBar() {
      return this.task && this.currentUserId && 
             (this.task.taskStatus === '0' || this.task.taskStatus === '1')
    }
  },
  
  onLoad(options) {
    this.taskId = options.id
    this.getCurrentUser()
    this.loadTaskDetail()
  },
  
  methods: {
    getCurrentUser() {
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        this.currentUserId = userInfo.userId
      }
    },
    
    async loadTaskDetail() {
      try {
        const res = await getTaskDetail(this.taskId)
        this.task = res.data
      } catch (error) {
        console.error('加载任务详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    async acceptTask() {
      if (!checkLogin()) return
      
      uni.showModal({
        title: '确认接取',
        content: '确定要接取这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            this.actionLoading = true
            try {
              await acceptTask(this.taskId)
              uni.showToast({
                title: '接取成功',
                icon: 'success'
              })
              this.loadTaskDetail()
            } catch (error) {
              console.error('接取任务失败:', error)
            } finally {
              this.actionLoading = false
            }
          }
        }
      })
    },
    
    async completeTask() {
      uni.showModal({
        title: '确认完成',
        content: '确定已完成这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            this.actionLoading = true
            try {
              await completeTask(this.taskId)
              uni.showToast({
                title: '任务已完成',
                icon: 'success'
              })
              this.loadTaskDetail()
            } catch (error) {
              console.error('完成任务失败:', error)
            } finally {
              this.actionLoading = false
            }
          }
        }
      })
    },
    
    async cancelTask() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            this.actionLoading = true
            try {
              await cancelTask(this.taskId)
              uni.showToast({
                title: '任务已取消',
                icon: 'success'
              })
              this.loadTaskDetail()
            } catch (error) {
              console.error('取消任务失败:', error)
            } finally {
              this.actionLoading = false
            }
          }
        }
      })
    },
    
    editTask() {
      uni.navigateTo({
        url: `/pages/task/publish?id=${this.taskId}`
      })
    },
    
    makeCall() {
      // 这里应该从后端获取联系方式
      uni.showToast({
        title: '联系功能开发中',
        icon: 'none'
      })
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': '待接取',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'waiting',
        '1': 'processing',
        '2': 'completed',
        '3': 'cancelled'
      }
      return classMap[status] || ''
    },
    
    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.task-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.detail-scroll {
  flex: 1;
  padding: 40rpx;
}

.task-info {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .user-info {
      display: flex;
      align-items: center;
      
      .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50rpx;
        margin-right: 20rpx;
      }
      
      .user-detail {
        .username {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 10rpx;
        }
        
        .publish-info {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
    
    .task-status {
      padding: 15rpx 30rpx;
      border-radius: 30rpx;
      font-size: 26rpx;
      
      &.waiting {
        background: #e8f5e8;
        color: #3cc51f;
      }
      
      &.processing {
        background: #fff3e0;
        color: #ff9800;
      }
      
      &.completed {
        background: #e3f2fd;
        color: #2196f3;
      }
      
      &.cancelled {
        background: #ffebee;
        color: #f44336;
      }
    }
  }
  
  .task-amount {
    font-size: 48rpx;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 20rpx;
  }
  
  .task-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    line-height: 1.4;
  }
  
  .task-desc {
    font-size: 30rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 30rpx;
  }
  
  .task-meta {
    .meta-item {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;
      
      .meta-text {
        margin-left: 15rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }
  
  .urgent-tag {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b35, #ff4757);
    color: #ffffff;
    padding: 15rpx 30rpx;
    border-radius: 30rpx;
    font-size: 26rpx;
    margin-top: 20rpx;
  }
}

.receiver-info, .contact-info {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .receiver-detail {
    .receiver-name {
      display: block;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .receive-time {
      font-size: 26rpx;
      color: #999;
    }
  }
  
  .contact-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    
    .contact-text {
      margin-left: 15rpx;
      font-size: 30rpx;
      color: #3cc51f;
    }
  }
}

.action-bar {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  
  .action-buttons {
    display: flex;
    gap: 20rpx;
    
    .u-button {
      flex: 1;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}
</style>
