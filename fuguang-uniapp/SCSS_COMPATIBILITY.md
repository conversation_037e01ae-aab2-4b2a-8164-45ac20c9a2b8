# SCSS 兼容性修复指南

## 🔧 问题背景

Vue2 SCSS 预编译器已从 node-sass 更换为 dart-sass，部分旧语法不再支持，需要进行兼容性修复。

## 🚨 已修复的问题

### 1. 深度选择器语法修复

**问题**: 登录页面使用了 `/deep/` 语法
**位置**: `pages/login/login.vue`
**修复**: 已将 `/deep/` 替换为 `:deep()`

```scss
// ❌ 旧语法 (不兼容 dart-sass)
/deep/ .u-input__content {
  height: 90rpx;
}

// ✅ 新语法 (兼容 dart-sass)
:deep(.u-input__content) {
  height: 90rpx;
}
```

## 📋 需要注意的语法差异

### 深度选择器语法对比

| 语法 | node-sass | dart-sass | 推荐使用 |
|------|-----------|-----------|----------|
| `/deep/` | ✅ | ❌ | ❌ |
| `>>>` | ✅ | ❌ | ❌ |
| `::v-deep` | ✅ | ⚠️ | ❌ |
| `:deep()` | ❌ | ✅ | ✅ |

### Vue 3 推荐语法

```scss
// Vue 3 + dart-sass 推荐语法
:deep(.child-class) {
  color: red;
}

:slotted(.slot-class) {
  color: blue;
}

:global(.global-class) {
  color: green;
}
```

## 🔍 项目中的潜在问题

### 1. uView UI 组件库
**位置**: `uni_modules/uview-ui/components/u-parse/u-parse.vue`
**问题**: 使用了 `>>>` 语法
**状态**: 第三方组件库，建议升级到兼容版本

### 2. 其他可能的问题语法

```scss
// ❌ 可能不兼容的语法
@import "~some-package/style.scss";  // 波浪号导入
/deep/ .class {}                     // 深度选择器
>>> .class {}                        // 深度选择器
::v-deep .class {}                   // Vue 2 深度选择器

// ✅ 兼容的语法
@import "some-package/style.scss";   // 直接导入
:deep(.class) {}                     // Vue 3 深度选择器
```

## 🛠️ 修复建议

### 1. 立即修复
- ✅ 已修复登录页面的 `/deep/` 语法
- 检查其他页面是否有类似问题

### 2. 长期优化
- 升级 uView UI 到兼容 dart-sass 的版本
- 统一使用 `:deep()` 语法
- 避免使用波浪号导入路径

### 3. 检查清单

```bash
# 搜索项目中可能的不兼容语法
grep -r "/deep/" src/
grep -r ">>>" src/
grep -r "::v-deep" src/
grep -r "@import.*~" src/
```

## 📝 最佳实践

### 1. 深度选择器使用
```scss
// 推荐：使用 :deep() 函数
.parent {
  :deep(.child) {
    color: red;
  }
}

// 避免：使用旧语法
.parent {
  /deep/ .child {
    color: red;
  }
}
```

### 2. 导入语句
```scss
// 推荐：直接路径导入
@import "uview-ui/theme.scss";

// 避免：波浪号导入
@import "~uview-ui/theme.scss";
```

### 3. 变量使用
```scss
// 推荐：使用 CSS 自定义属性
:root {
  --primary-color: #667eea;
}

.button {
  background: var(--primary-color);
}

// 或使用 SCSS 变量
$primary-color: #667eea;

.button {
  background: $primary-color;
}
```

## 🔧 自动化修复脚本

创建一个简单的修复脚本：

```bash
#!/bin/bash
# fix-scss-compatibility.sh

echo "🔧 修复 SCSS 兼容性问题..."

# 替换 /deep/ 语法
find . -name "*.vue" -o -name "*.scss" | xargs sed -i 's|/deep/|:deep|g'

# 替换 >>> 语法
find . -name "*.vue" -o -name "*.scss" | xargs sed -i 's|>>>|:deep|g'

# 替换 ::v-deep 语法
find . -name "*.vue" -o -name "*.scss" | xargs sed -i 's|::v-deep|:deep|g'

echo "✅ 修复完成！"
```

## 📚 参考资料

- [Sass: Breaking Changes](https://sass-lang.com/documentation/breaking-changes)
- [Vue 3 SFC CSS Features](https://vuejs.org/api/sfc-css-features.html)
- [dart-sass vs node-sass](https://github.com/sass/dart-sass#compatibility)

## ⚠️ 注意事项

1. **第三方组件库**: 如 uView UI 可能仍使用旧语法，建议升级到兼容版本
2. **渐进式修复**: 可以逐步修复，不需要一次性全部更改
3. **测试验证**: 修复后需要测试样式是否正常显示
4. **版本控制**: 修复前建议提交当前代码，便于回滚

## 🎯 当前状态

- ✅ 登录页面 `/deep/` 语法已修复
- ⚠️ uView UI 组件库使用旧语法（第三方库）
- 🔍 其他页面需要进一步检查

修复完成后，项目应该能够正常使用 dart-sass 编译器。
