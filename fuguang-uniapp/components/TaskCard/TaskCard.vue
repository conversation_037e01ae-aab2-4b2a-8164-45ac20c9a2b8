<template>
  <view class="task-card" @click="handleClick">
    <view class="task-header">
      <view class="user-info">
        <image class="avatar" :src="task.publisherAvatar || '/static/default-avatar.png'" mode="aspectFill"></image>
        <view class="user-detail">
          <text class="username">{{ task.publisherName }}</text>
          <text class="publish-info">{{ formatTime(task.createTime) }} · {{ task.taskAddress }}</text>
        </view>
      </view>
      <view class="task-status" :class="getStatusClass(task.taskStatus)">
        {{ getStatusText(task.taskStatus) }}
      </view>
    </view>
    
    <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
    <view class="task-title">{{ task.taskTitle }}</view>
    <view class="task-desc">{{ task.taskDesc }}</view>
    
    <view class="task-footer">
      <view class="task-meta">
        <text class="view-count">{{ task.viewCount || 0 }}次浏览</text>
        <text class="hot-score" v-if="task.hotScore > 0">🔥{{ task.hotScore }}</text>
      </view>
      <view class="task-type" v-if="task.taskType === '1'">
        <text class="urgent-tag">紧急</text>
      </view>
    </view>
  </view>
</template>

<script>
import { formatTime, formatMoney } from '@/utils/common'

export default {
  name: 'TaskCard',
  props: {
    task: {
      type: Object,
      required: true
    }
  },
  
  methods: {
    handleClick() {
      this.$emit('click', this.task)
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': '待接取',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'waiting',
        '1': 'processing',
        '2': 'completed',
        '3': 'cancelled'
      }
      return classMap[status] || ''
    },
    
    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.task-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .user-info {
      display: flex;
      align-items: center;
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        margin-right: 20rpx;
      }
      
      .user-detail {
        .username {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 5rpx;
        }
        
        .publish-info {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .task-status {
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      
      &.waiting {
        background: #e8f5e8;
        color: #3cc51f;
      }
      
      &.processing {
        background: #fff3e0;
        color: #ff9800;
      }
      
      &.completed {
        background: #e3f2fd;
        color: #2196f3;
      }
      
      &.cancelled {
        background: #ffebee;
        color: #f44336;
      }
    }
  }
  
  .task-amount {
    font-size: 36rpx;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 10rpx;
  }
  
  .task-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
    line-height: 1.4;
  }
  
  .task-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 20rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  
  .task-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .task-meta {
      display: flex;
      gap: 20rpx;
      
      .view-count, .hot-score {
        font-size: 24rpx;
        color: #999;
      }
      
      .hot-score {
        color: #ff6b35;
      }
    }
    
    .urgent-tag {
      background: #ff4757;
      color: #ffffff;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 22rpx;
    }
  }
}
</style>
