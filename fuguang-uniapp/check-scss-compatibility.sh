#!/bin/bash

echo "🔍 检查 SCSS 兼容性..."
echo "=================================="

# 检查项目中是否存在不兼容的语法
echo "📋 检查不兼容的深度选择器语法..."

# 检查 /deep/ 语法
DEEP_COUNT=$(find . -name "*.vue" -o -name "*.scss" | grep -v node_modules | grep -v uni_modules | xargs grep -c "/deep/" 2>/dev/null | awk -F: '{sum += $2} END {print sum+0}')

# 检查 >>> 语法
ARROW_COUNT=$(find . -name "*.vue" -o -name "*.scss" | grep -v node_modules | grep -v uni_modules | xargs grep -c ">>>" 2>/dev/null | awk -F: '{sum += $2} END {print sum+0}')

# 检查 ::v-deep 语法
V_DEEP_COUNT=$(find . -name "*.vue" -o -name "*.scss" | grep -v node_modules | grep -v uni_modules | xargs grep -c "::v-deep" 2>/dev/null | awk -F: '{sum += $2} END {print sum+0}')

echo ""
echo "📊 检查结果："
echo "--------------------------------"
echo "❌ /deep/ 语法使用次数: $DEEP_COUNT"
echo "❌ >>> 语法使用次数: $ARROW_COUNT"
echo "❌ ::v-deep 语法使用次数: $V_DEEP_COUNT"

TOTAL_ISSUES=$((DEEP_COUNT + ARROW_COUNT + V_DEEP_COUNT))

if [ $TOTAL_ISSUES -eq 0 ]; then
    echo ""
    echo "✅ 恭喜！项目中没有发现不兼容的 SCSS 语法"
    echo "✅ 项目已兼容 dart-sass 编译器"
else
    echo ""
    echo "⚠️  发现 $TOTAL_ISSUES 个不兼容的语法问题"
    echo ""
    echo "🔧 建议修复方案："
    echo "1. 将 /deep/ 替换为 :deep()"
    echo "2. 将 >>> 替换为 :deep()"
    echo "3. 将 ::v-deep 替换为 :deep()"
    echo ""
    echo "📝 详细信息请查看 SCSS_COMPATIBILITY.md"
fi

echo ""
echo "🔍 检查第三方组件库..."
echo "--------------------------------"

# 检查第三方组件库中的不兼容语法（仅提示）
THIRD_PARTY_ISSUES=$(find uni_modules node_modules -name "*.vue" -o -name "*.scss" 2>/dev/null | xargs grep -l "/deep/\|>>>\|::v-deep" 2>/dev/null | wc -l)

if [ $THIRD_PARTY_ISSUES -gt 0 ]; then
    echo "⚠️  第三方组件库中发现 $THIRD_PARTY_ISSUES 个文件使用旧语法"
    echo "💡 这是正常的，第三方库的兼容性由库作者负责"
    echo "💡 如遇到编译问题，建议升级到兼容版本"
else
    echo "✅ 第三方组件库检查完成"
fi

echo ""
echo "🎯 总结："
echo "=================================="
if [ $TOTAL_ISSUES -eq 0 ]; then
    echo "✅ 项目 SCSS 语法完全兼容 dart-sass"
    echo "🚀 可以安全使用 dart-sass 编译器"
else
    echo "⚠️  需要修复 $TOTAL_ISSUES 个语法问题"
    echo "📖 请参考 SCSS_COMPATIBILITY.md 进行修复"
fi

echo ""
echo "📚 相关文档："
echo "- SCSS_COMPATIBILITY.md - 兼容性修复指南"
echo "- LOGIN_DESIGN.md - 登录页面设计说明"
echo ""
