<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>浮光登录页面预览</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .phone-frame {
        width: 375px;
        height: 667px;
        background: #000;
        border-radius: 30px;
        padding: 10px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      }

      .screen {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        overflow: hidden;
        position: relative;
      }

      .bg-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
      }

      .bg-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
      }

      .circle-1 {
        width: 100px;
        height: 100px;
        top: 10%;
        right: -25px;
      }

      .circle-2 {
        width: 75px;
        height: 75px;
        top: 60%;
        left: -15px;
        animation-delay: -2s;
      }

      .circle-3 {
        width: 50px;
        height: 50px;
        top: 30%;
        left: 20%;
        animation-delay: -4s;
      }

      .content {
        position: relative;
        z-index: 10;
        padding: 40px 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
      }

      .logo {
        width: 70px;
        height: 70px;
        background: #fff;
        border-radius: 50%;
        margin: 0 auto 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #667eea;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .title {
        font-size: 26px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 8px;
        text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
      }

      .subtitle {
        font-size: 15px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 10px;
      }

      .title-underline {
        width: 40px;
        height: 2px;
        background: linear-gradient(90deg, transparent, #fff, transparent);
        margin: 0 auto;
        border-radius: 1px;
      }

      .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 30px 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .login-tabs {
        display: flex;
        background: #f5f5f5;
        border-radius: 8px;
        padding: 3px;
        margin-bottom: 20px;
      }

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 10px 0;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        color: #666;
      }

      .tab-item.active {
        background: #fff;
        color: #667eea;
        font-weight: bold;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      .input-group {
        margin-bottom: 15px;
      }

      .input-wrapper {
        position: relative;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid transparent;
        transition: all 0.3s ease;
      }

      .input-wrapper:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
      }

      .input-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 16px;
      }

      .input-field {
        width: 100%;
        padding: 15px 15px 15px 35px;
        border: none;
        background: transparent;
        font-size: 15px;
        outline: none;
      }

      .sms-wrapper {
        display: flex;
        align-items: center;
      }

      .sms-input {
        flex: 1;
        padding-right: 80px;
      }

      .sms-btn {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        background: #667eea;
        color: #fff;
        padding: 8px 12px;
        border-radius: 5px;
        font-size: 12px;
        cursor: pointer;
        border: none;
      }

      .sms-btn.disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .forgot-password {
        text-align: right;
        margin-bottom: 15px;
      }

      .forgot-password a {
        color: #667eea;
        text-decoration: none;
        font-size: 13px;
      }

      .agreement-row {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 12px;
        flex-wrap: wrap;
      }

      .agreement-text {
        color: #666;
        margin: 0 2px;
      }

      .agreement-link {
        color: #667eea;
        text-decoration: none;
        margin: 0 2px;
      }

      .checkbox {
        margin-right: 5px;
      }

      .login-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(102, 126, 234, 0.3);
      }

      .login-btn:disabled {
        background: linear-gradient(135deg, #ccc 0%, #999 100%);
        box-shadow: none;
        cursor: not-allowed;
      }

      .divider {
        display: flex;
        align-items: center;
        margin: 20px 0;
      }

      .divider-line {
        flex: 1;
        height: 1px;
        background: #e0e0e0;
      }

      .divider-text {
        margin: 0 10px;
        font-size: 12px;
        color: #999;
      }

      .guest-mode {
        text-align: center;
        margin-top: 15px;
      }

      .guest-text {
        color: #667eea;
        text-decoration: underline;
        font-size: 14px;
        cursor: pointer;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      .hidden {
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="phone-frame">
      <div class="screen">
        <div class="bg-decoration">
          <div class="bg-circle circle-1"></div>
          <div class="bg-circle circle-2"></div>
          <div class="bg-circle circle-3"></div>
        </div>

        <div class="content">
          <div class="header">
            <div class="logo">浮</div>
            <div class="title">欢迎使用浮光</div>
            <div class="subtitle">未注册的手机号登录后会自动创建账号</div>
            <div class="title-underline"></div>
          </div>

          <div class="form-container">
            <div class="login-tabs">
              <div class="tab-item active" onclick="switchTab('password')">
                密码登录
              </div>
              <div class="tab-item" onclick="switchTab('sms')">验证码登录</div>
            </div>

            <div class="input-group">
              <div class="input-wrapper">
                <div class="input-icon">📱</div>
                <input
                  type="tel"
                  class="input-field"
                  placeholder="请输入手机号"
                  maxlength="11"
                />
              </div>
            </div>

            <div class="input-group" id="password-input">
              <div class="input-wrapper">
                <div class="input-icon">🔒</div>
                <input
                  type="password"
                  class="input-field"
                  placeholder="请输入密码"
                />
              </div>
            </div>

            <div class="input-group hidden" id="sms-input">
              <div class="input-wrapper sms-wrapper">
                <div class="input-icon">✓</div>
                <input
                  type="number"
                  class="input-field sms-input"
                  placeholder="请输入验证码"
                  maxlength="6"
                />
                <button class="sms-btn" onclick="sendSms()">获取验证码</button>
              </div>
            </div>

            <div class="forgot-password" id="forgot-password">
              <a href="#" onclick="forgotPassword()">忘记密码？</a>
            </div>

            <div class="agreement-row">
              <input
                type="checkbox"
                class="checkbox"
                id="agreement"
                onchange="checkAgreement()"
              />
              <span class="agreement-text">我已阅读并同意</span>
              <a
                href="#"
                class="agreement-link"
                onclick="showAgreement('service')"
                >《服务协议》</a
              >
              <span class="agreement-text">和</span>
              <a
                href="#"
                class="agreement-link"
                onclick="showAgreement('privacy')"
                >《隐私协议》</a
              >
            </div>

            <button class="login-btn" disabled onclick="login()">登录</button>

            <div class="divider">
              <div class="divider-line"></div>
              <span class="divider-text">或</span>
              <div class="divider-line"></div>
            </div>

            <div class="guest-mode">
              <span class="guest-text" onclick="guestMode()"
                >游客模式，继续使用</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let currentTab = "password";
      let smsCountdown = 0;
      let smsTimer = null;

      function switchTab(tab) {
        currentTab = tab;

        // 更新标签样式
        document.querySelectorAll(".tab-item").forEach((item) => {
          item.classList.remove("active");
        });
        event.target.classList.add("active");

        // 切换输入框
        if (tab === "password") {
          document.getElementById("password-input").classList.remove("hidden");
          document.getElementById("sms-input").classList.add("hidden");
          document.getElementById("forgot-password").classList.remove("hidden");
        } else {
          document.getElementById("password-input").classList.add("hidden");
          document.getElementById("sms-input").classList.remove("hidden");
          document.getElementById("forgot-password").classList.add("hidden");
        }
      }

      function sendSms() {
        if (smsCountdown > 0) return;

        const phoneInput = document.querySelector('input[type="tel"]');
        const phone = phoneInput.value;

        if (!phone) {
          alert("请输入手机号");
          return;
        }

        if (!/^1[3-9]\d{9}$/.test(phone)) {
          alert("请输入正确的手机号");
          return;
        }

        // 模拟发送验证码
        alert("验证码已发送");
        startCountdown();
      }

      function startCountdown() {
        smsCountdown = 60;
        const btn = document.querySelector(".sms-btn");
        btn.classList.add("disabled");

        smsTimer = setInterval(() => {
          smsCountdown--;
          btn.textContent = smsCountdown + "s";

          if (smsCountdown <= 0) {
            clearInterval(smsTimer);
            btn.classList.remove("disabled");
            btn.textContent = "获取验证码";
          }
        }, 1000);
      }

      function checkAgreement() {
        const checkbox = document.getElementById("agreement");
        const loginBtn = document.querySelector(".login-btn");

        if (checkbox.checked) {
          showPrivacyModal();
          loginBtn.disabled = false;
        } else {
          loginBtn.disabled = true;
        }
      }

      function showPrivacyModal() {
        const result =
          confirm(`感谢您使用浮光APP，我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，您在使用我们产品前，希望您确认以下关于用户权限的说明：

1、我们会根据您使用服务的具体功能需要，收集用户信息（涉及设备信息、账户等），并且严格遵循隐私政策收集、使用这些信息。

2、您可以在"我的"页面或相应的服务页管理您的个人信息。

3、我们的信息只会存储在您设备的本地以及我们在中国大陆境内的服务器中。

4、我们会尽全力保护您的信息安全。

点击"确定"表示同意并继续，点击"取消"将进入游客模式。`);

        if (!result) {
          document.getElementById("agreement").checked = false;
          document.querySelector(".login-btn").disabled = true;
          guestMode();
        }
      }

      function showAgreement(type) {
        const title = type === "service" ? "服务协议" : "隐私协议";
        const content =
          type === "service"
            ? "这里是服务协议的内容..."
            : "这里是隐私协议的内容...";
        alert(title + "\n\n" + content);
      }

      function forgotPassword() {
        alert("请联系客服或通过注册手机号找回密码");
      }

      function login() {
        const phone = document.querySelector('input[type="tel"]').value;

        if (!phone) {
          alert("请输入手机号");
          return;
        }

        if (currentTab === "password") {
          const password = document.querySelector(
            'input[type="password"]'
          ).value;
          if (!password) {
            alert("请输入密码");
            return;
          }
        } else {
          const smsCode = document.querySelector(".sms-input").value;
          if (!smsCode) {
            alert("请输入验证码");
            return;
          }
        }

        alert("登录成功！");
      }

      function guestMode() {
        const result = confirm(
          '游客模式下功能受限，建议注册登录获得完整体验\n\n点击"确定"继续游客模式'
        );
        if (result) {
          alert("已进入游客模式");
        }
      }
    </script>
  </body>
</html>
