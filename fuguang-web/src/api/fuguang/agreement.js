import request from '@/utils/request'

// 查询APP配置列表
export function listAppConfig(query) {
  return request({
    url: '/fuguang/config/list',
    method: 'get',
    params: query
  })
}

// 查询APP配置详细
export function getAppConfig(configId) {
  return request({
    url: '/fuguang/config/' + configId,
    method: 'get'
  })
}

// 新增APP配置
export function addAppConfig(data) {
  return request({
    url: '/fuguang/config',
    method: 'post',
    data: data
  })
}

// 修改APP配置
export function updateAppConfig(data) {
  return request({
    url: '/fuguang/config',
    method: 'put',
    data: data
  })
}

// 删除APP配置
export function delAppConfig(configId) {
  return request({
    url: '/fuguang/config/' + configId,
    method: 'delete'
  })
}

// 获取用户协议
export function getUserAgreement() {
  return request({
    url: '/app/agreement/user',
    method: 'get'
  })
}

// 获取隐私协议
export function getPrivacyPolicy() {
  return request({
    url: '/app/agreement/privacy',
    method: 'get'
  })
}
