<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="配置名称" prop="configName">
        <el-input v-model="queryParams.configName" placeholder="请输入配置名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="配置键名" prop="configKey">
        <el-input v-model="queryParams.configKey" placeholder="请输入配置键名" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['fuguang:config:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-document" size="mini" @click="handleAddUserAgreement"
          v-hasPermi="['fuguang:config:add']">添加用户协议</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-lock" size="mini" @click="handleAddPrivacyPolicy"
          v-hasPermi="['fuguang:config:add']">添加隐私协议</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['fuguang:config:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['fuguang:config:remove']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <el-table-column label="配置主键" align="center" prop="configId" />
      <el-table-column label="配置名称" align="center" prop="configName" :show-overflow-tooltip="true" />
      <el-table-column label="配置键名" align="center" prop="configKey" :show-overflow-tooltip="true" />
      <el-table-column label="配置键值" align="center" prop="configValue" :show-overflow-tooltip="true" width="200" />
      <el-table-column label="系统内置" align="center" prop="configType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.configType" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['fuguang:config:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['fuguang:config:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改APP配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键名" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入配置键名" :disabled="form.configId != null" />
        </el-form-item>
        <el-form-item label="配置键值" prop="configValue">
          <el-input v-model="form.configValue" type="textarea" :rows="8" placeholder="请输入配置键值" />
        </el-form-item>
        <el-form-item label="系统内置" prop="configType">
          <el-radio-group v-model="form.configType">
            <el-radio v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.value">{{ dict.label
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAppConfig, getAppConfig, delAppConfig, addAppConfig, updateAppConfig } from "@/api/fuguang/agreement"

export default {
  name: "Agreement",
  dicts: ['sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // APP配置表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: null,
        configKey: null,
        configType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configName: [
          { required: true, message: "配置名称不能为空", trigger: "blur" }
        ],
        configKey: [
          { required: true, message: "配置键名不能为空", trigger: "blur" }
        ],
        configValue: [
          { required: true, message: "配置键值不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询APP配置列表 */
    getList() {
      this.loading = true
      // 只查询协议相关的配置
      const params = {
        ...this.queryParams
      }
      listAppConfig(params).then(response => {
        this.configList = response.rows;
        this.total = this.configList.length
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        configId: null,
        configName: null,
        configKey: null,
        configValue: null,
        configType: "Y",
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加APP配置"
    },
    /** 添加用户协议 */
    handleAddUserAgreement() {
      this.reset()
      this.form.configName = "用户协议"
      this.form.configKey = "app.user.agreement"
      this.form.configValue = `# 用户协议

欢迎使用浮光APP！

## 1. 服务条款
本协议是您与浮光APP之间关于使用浮光APP服务的法律协议。

## 2. 用户权利和义务
2.1 您有权使用浮光APP提供的各项服务
2.2 您应当遵守相关法律法规，不得利用本服务从事违法活动

## 3. 服务内容
3.1 任务发布与接取服务
3.2 用户信息管理服务
3.3 其他相关服务

## 4. 隐私保护
我们重视您的隐私保护，详细内容请参阅《隐私协议》

## 5. 免责声明
5.1 平台仅提供信息撮合服务
5.2 用户自行承担交易风险

## 6. 协议修改
我们保留随时修改本协议的权利，修改后的协议将在APP内公布。

如有疑问，请联系客服：400-888-8888`
      this.form.configType = "Y"
      this.form.remark = "APP用户协议配置"
      this.open = true
      this.title = "添加用户协议"
    },
    /** 添加隐私协议 */
    handleAddPrivacyPolicy() {
      this.reset()
      this.form.configName = "隐私协议"
      this.form.configKey = "app.privacy.policy"
      this.form.configValue = `# 隐私协议

感谢您使用浮光APP，我们非常重视您的个人信息和隐私保护。

## 1. 信息收集
我们会收集您在使用浮光APP时提供的信息，包括但不限于：
- 注册信息：用户名、密码、手机号、邮箱等
- 个人资料：头像、昵称、性别、地址等
- 位置信息：用于任务匹配和地理位置服务
- 设备信息：设备型号、操作系统版本等

## 2. 信息使用
我们使用收集的信息用于：
- 提供和改进我们的服务
- 处理您的任务发布和接取请求
- 发送重要通知和服务更新
- 保护用户安全和防止欺诈

## 3. 信息保护
我们采取适当的安全措施保护您的个人信息：
- 使用加密技术保护数据传输
- 限制员工访问个人信息
- 定期审查安全措施
- 遵守相关法律法规要求

## 4. 信息共享
除法律要求外，我们不会向第三方分享您的个人信息。

## 5. 您的权利
您有权访问、更正、删除您的个人信息。

## 6. 联系我们
如果您对本隐私政策有任何疑问，请联系我们：
- 邮箱：<EMAIL>
- 电话：400-888-8888`
      this.form.configType = "Y"
      this.form.remark = "APP隐私协议配置"
      this.open = true
      this.title = "添加隐私协议"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const configId = row.configId || this.ids
      getAppConfig(configId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改APP配置"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != null) {
            updateAppConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addAppConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids
      this.$modal.confirm('是否确认删除APP配置编号为"' + configIds + '"的数据项？').then(function () {
        return delAppConfig(configIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => { })
    }
  }
}
</script>
